from .common import *
from os import getenv

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

SECRET_KEY = os.environ['SECRET_KEY']

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql',
#         'NAME': getenv('PGDATABASE'),
#         'USER': getenv('PGUSER'),
#         'PASSWORD': getenv('PGPASSWORD'),
#         'HOST': getenv('PGHOST'),
#         'PORT': getenv('PGPORT', 5432),
#         'OPTIONS': {
#             'sslmode': 'require',
#         },
#     }
# }

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'koyebdb',
        'USER': 'koyeb-adm',
        'PASSWORD': 'npg_IE0ByMrODQ7o',
        'HOST': 'ep-soft-unit-a1lfnuch.ap-southeast-1.pg.koyeb.app',
        'OPTIONS': {'sslmode': 'require'},
    }
}

# CACHES = {
#     "default": {
#         "BACKEND": "custom_cache.UpstashRedisCache",
#         "LOCATION": "upstash-redis://",
#         "OPTIONS": {
#             "URL": getenv("UPSTASH_REDIS_REST_URL"),
#             "TOKEN": getenv("UPSTASH_REDIS_REST_TOKEN"),
#         }
#     }
# }

# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#         }
#     }
# }

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

AUTH_COOKIE_SECURE = True  # on production

# HTTPS settings
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True

# SECURE_SSL_REDIRECT = True

# HSTS settings
# SECURE_HSTS_SECONDS = 31536000 # 1 year
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_BROWSER_XSS_FILTER = True
# SECURE_CONTENT_TYPE_NOSNIFF = True

# Static files settings for production
# STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Optional WhiteNoise optimizations
# WHITENOISE_MAX_AGE = 31536000  # Cache static files for 1 year (if immutable)
# WHITENOISE_KEEP_ONLY_HASHED_FILES = True  # Only keep hashed versions of files

# when debugging is turned off it is required to set ALLOWED_HOSTS
# ALLOWED_HOSTS = getenv('ALLOWED_HOSTS').split(' ')

ALLOWED_HOSTS = [
    # '.koyeb.app',
    # '.onrender.com',
    # '127.0.0.1',
    'ec2-3-0-95-43.ap-southeast-1.compute.amazonaws.com',
    '*********',
    'localhost',
    'picky-api.space',
]

CSRF_TRUSTED_ORIGINS = [
    'http://*********:8000',
    'http://ec2-3-0-95-43.ap-southeast-1.compute.amazonaws.com:8000',
    'https://picky-api.space'
]

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    # 'formatters': {
    #     'verbose': {
    #         'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
    #     },
    #     'simple': {
    #         'format': '%(levelname)s %(message)s'
    #     },
    # },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
        },
        # 'file': {
        #     # 'level': 'DEBUG',
        #     'class': 'logging.FileHandler',
        #     'filename': 'general.log',
        #     'formatter': 'verbose'
        # },
        # 'mail_admins': {
        #     'level': 'ERROR',
        #     'class': 'django.utils.log.AdminEmailHandler'
        # }
    },
    'loggers': {
        '': {
            'handlers': ['console'],
            'level': os.environ.get('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': True,
        },
        # 'django.request': {
        #     'handlers': ['mail_admins'],
        #     'level': 'ERROR',
        #     'propagate': False,
        # },
    }
}
