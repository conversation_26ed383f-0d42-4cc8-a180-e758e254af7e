/* Custom Admin CSS */

/* General admin customizations */
.admin-custom {
    /* Add any general admin customizations here */
}

/* Drag and drop enhancements for product variants */
.sortable-tabular-inline .drag-handle {
    cursor: move;
    color: #666;
    font-size: 16px;
    padding: 8px 5px;
    text-align: center;
    width: 30px;
    user-select: none;
    font-weight: bold;
    letter-spacing: -2px;
}

.sortable-tabular-inline .drag-handle:hover {
    color: #333;
    background-color: #e9ecef;
    border-radius: 3px;
}

/* Enhanced visual feedback for dragging */
.sortable-tabular-inline tbody tr.drag-over {
    background-color: #fff3cd !important;
    border-top: 3px solid #ffc107 !important;
    border-bottom: 3px solid #ffc107 !important;
}

.sortable-tabular-inline tbody tr.sortable-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    transition: background-color 0.3s ease;
}

/* Order field styling */
.field-order input {
    width: 60px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-weight: bold;
    color: #495057;
}

/* Drag handle column */
.field-drag_handle {
    width: 40px;
    padding: 0 !important;
}

.field-drag_handle th {
    width: 40px;
    padding: 8px 4px !important;
    text-align: center;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}
