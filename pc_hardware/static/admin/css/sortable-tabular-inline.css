/* Sortable Tabular Inline CSS */

/* Make the table sortable */
.sortable-tabular-inline {
    position: relative;
}

/* Style for table rows */
.sortable-tabular-inline tbody tr {
    transition: background-color 0.2s ease;
}

.sortable-tabular-inline tbody tr:hover {
    background-color: #f8f9fa;
}

/* Drag handle styling */
.drag-handle {
    cursor: move;
    color: #666;
    font-size: 16px;
    padding: 8px 5px;
    text-align: center;
    width: 30px;
    user-select: none;
    font-weight: bold;
    letter-spacing: -2px;
}

.drag-handle:hover {
    color: #333;
    background-color: #e9ecef;
    border-radius: 3px;
}

/* Drag states for HTML5 drag and drop */
.sortable-tabular-inline tbody tr[draggable="true"] {
    cursor: move;
}

.sortable-tabular-inline tbody tr.drag-over {
    background-color: #fff3cd !important;
    border-top: 3px solid #ffc107 !important;
    border-bottom: 3px solid #ffc107 !important;
}

.sortable-tabular-inline tbody tr.drag-over td {
    border-color: #ffc107 !important;
}

/* Dragged item styling */
.sortable-tabular-inline tbody tr[style*="opacity: 0.5"] {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

.sortable-tabular-inline tbody tr[style*="opacity: 0.5"] td {
    border-color: #2196f3 !important;
}

/* Order field styling */
.field-order {
    width: 60px;
    text-align: center;
}

.field-order input {
    width: 50px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-weight: bold;
    color: #495057;
}

/* Drag handle column */
.field-drag_handle {
    width: 40px;
    padding: 0 !important;
}

.field-drag_handle th {
    width: 40px;
    padding: 8px 4px !important;
    text-align: center;
}

/* Hide empty form row during sorting */
.form-row.empty-form {
    display: none;
}

/* Loading state */
.sortable-updating {
    opacity: 0.7;
    pointer-events: none;
}

.sortable-updating::after {
    content: "Updating order...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 12px 24px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    font-weight: bold;
    color: #495057;
}

/* Success feedback */
.sortable-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    transition: background-color 0.3s ease;
}

/* Error feedback */
.sortable-error {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .drag-handle {
        font-size: 14px;
        padding: 6px 3px;
    }
    
    .field-drag_handle {
        width: 35px;
    }
    
    .field-order input {
        width: 45px;
        font-size: 12px;
    }
}

/* Ensure proper table layout */
.inline-group .tabular {
    overflow-x: auto;
}

.inline-group .tabular table {
    min-width: 100%;
}

/* Style for the drag handle header */
.field-drag_handle th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

/* Improve visual feedback during drag */
.ui-sortable-helper .drag-handle {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.1);
}

/* Ensure consistent row heights */
.sortable-tabular-inline tbody tr {
    min-height: 50px;
}

.sortable-tabular-inline tbody tr td {
    vertical-align: middle;
}
