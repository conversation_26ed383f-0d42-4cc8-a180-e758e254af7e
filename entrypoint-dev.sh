#!/bin/ash

# Set settings module explicitly (optional if set in Dockerfile or docker-compose)
# export DJANGO_SETTINGS_MODULE=pc_hardware.settings.dev

# Apply migrations (skip makemigrations for automatic runs)
echo "Applying database migrations..."
python manage.py migrate || exit 1

# Start server (use runserver for development with autoreload)
echo "Starting server..."
exec python -u manage.py runserver 0.0.0.0:${PORT:-8000}

