[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "*"
django-mptt = "*"
djangorestframework = "*"
drf-spectacular = "*"
django-cors-headers = "*"
django-filter = "*"
drf-nested-routers = "*"
djangorestframework-simplejwt = "*"
cloudinary = "*"
social-auth-app-django = "*"
psycopg2-binary = "*"
django-debug-toolbar = "*"
python-dotenv = "*"
gunicorn = "*"
whitenoise = "*"
stripe = "*"
django-mptt-admin = "*"
django-phonenumber-field = { extras = ["phonenumberslite"] }
twilio = "*"
vonage = "*"
phonenumbers = "*"
django-admin-interface = "*"
redis = "*"
django-redis = "*"
upstash-redis = "*"
celery = "*"
django-ordered-model = "*"
py3dbp = "*"
strawberry-django-plus = {extras = ["debug-server"], version = "*"}
strawberry-graphql-django = "*"

[dev-packages]
pytest = "*"
pytest-django = "*"
locust = "*"
pytest-watch = "*"
model-bakery = "*"

[requires]
python_version = "*"
python_full_version = "*"
