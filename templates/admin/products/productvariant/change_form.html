{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<style>
  /* Additional styles for the admin interface */
  .delete-inline-image {
    transition: background-color 0.3s;
  }

  .delete-inline-image:hover {
    background-color: #a41515 !important;
  }

  /* Hide any remaining delete checkboxes */
  .field-DELETE,
  td.delete,
  th.delete-header,
  input[name$="-DELETE"],
  label[for$="-DELETE"] {
    display: none !important;
  }
</style>
<script type="text/javascript">
  django.jQuery(document).ready(function($) {
    // Add a class to any header containing "Delete" text and hide it
    $('th').each(function() {
      if ($(this).text().trim() === 'Delete') {
        $(this).addClass('delete-header').hide();
      }
    });

    // Hide any delete-related inputs and their parent cells
    $('input[name$="-DELETE"]').parents('td').addClass('delete-checkbox-column').hide();
    $('input[name$="-DELETE"]').hide();
    $('label[for$="-DELETE"]').hide();
  });
</script>
{% endblock %}
