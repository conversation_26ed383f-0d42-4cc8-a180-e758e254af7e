import os


# Run this script like this: python scripts/delete_migrations.py

def delete_migration_files():
    for root, dirs, files in os.walk('.'):
        if 'migrations' in dirs:
            migration_dir = os.path.join(root, 'migrations')
            for filename in os.listdir(migration_dir):
                file_path = os.path.join(migration_dir, filename)
                if filename != '__init__.py' and filename.endswith('.py'):
                    print(f"Deleting migration file: {file_path}")
                    os.remove(file_path)
                elif filename.endswith('.pyc'):
                    print(f"Deleting compiled file: {file_path}")
                    os.remove(file_path)


if __name__ == "__main__":
    delete_migration_files()
