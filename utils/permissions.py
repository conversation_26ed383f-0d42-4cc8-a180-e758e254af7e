from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
import logging

logger = logging.getLogger(__name__)


class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        logger.debug("IsAdminOrReadOnly permission check")
        # if not request.user.is_authenticated:
        #     logger.warning("User not authenticated")
        #     print("IsAdminOrReadOnly called")
        #     return False
        print("User authenticated")
        if request.method in permissions.SAFE_METHODS:
            print("User authenticated")
            return True
        return bool(request.user and request.user.is_staff)


# Prevent unauthorized users from accessing data
class FullDjangoModelPermissions(permissions.DjangoModelPermissions):
    def __init__(self) -> None:
        self.perms_map['GET'] = ['%(app_label)s.view_%(model_name)s']


# class ViewCustomerHistoryPermission(permissions.BasePermission):
#     def has_permission(self, request, view):
#         return request.user.has_perm('store.view_history')


# NOTE: IsAdminOrGroupMember has been deprecated and moved to order app
# This class contained outdated role references and has been replaced with
# customer-focused permissions in apps/order/permissions.py
# Staff operations are now handled by the staff app with proper RBAC


class IsAdminOrOwner(permissions.BasePermission):
    def has_permission(self, request, view):
        # Allow only authenticated users
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admins can access any object
        if request.user.is_staff or request.user.is_superuser:
            return True
        # Regular users can only access their own profile
        return (obj.user or obj.customer.user) == request.user


class IsAdminOrCustomer(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admins can access any object
        if request.user.is_staff or request.user.is_superuser:
            return True
        # Regular users can only access their own profile
        return obj.customer.user == request.user
