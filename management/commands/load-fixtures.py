from django.core.management import call_command
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    def handle(self, *args, **options):
        # call_command('makemigrations')
        # call_command('migrate')
        # call_command('loaddata', 'tests/fixtures/admin.json')
        call_command('loaddata', 'tests/store/fixtures/category.json')
        call_command('loaddata', 'tests/store/fixtures/attribute.json')
        call_command('loaddata', 'tests/store/fixtures/attribute_value.json')
        call_command('loaddata', 'tests/store/fixtures/product_type.json')
        call_command('loaddata', 'tests/store/fixtures/product_type_attribute.json')
        # call_command('loaddata', 'tests/fixtures/product.json')

# To load data, run this file:
# python manage.py load-fixtures
