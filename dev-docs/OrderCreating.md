### Creating an Order with Cart

1. The client sends a **POST** request to the **OrderViewSet** to create a new order. The request body includes:
    - cart_id: UUID of the cart to be converted into an order.
    - selected_address: ID of the shipping address chosen by the customer.
    - order_status: Delivery status of the order (default is "Pending").
    - payment_method: ID of the payment option used for this order.

2. On OrderViewSet.create Method is Called:
    - A CreateOrderSerializer is instantiated with the data from the request and the user_id
      (which is passed via context to the serializer).
    - The is_valid method of CreateOrderSerializer is called to validate the input data.
        - The cart_id field is validated to check if the cart exists.
        - It checks whether the cart contains any items. If the cart is empty, a validation error is raised.
    - .save Method is Called:
        - Once the data is valid, the .save method is called to actually create the order.
    - The created order data is returned to the client.

3. Here's what happens inside save:
    - A transaction is started using transaction.atomic(). This ensures that all operations
      (order creation, order items creation, cart deletion) either succeed together or fail together.
    - The customer is fetched using the **user_id** passed in the serializer's context.
    - The cart items are retrieved from the database using the provided cart_id.
    - The total cost of the order is calculated by summing up the price of each product variant in the cart items.
    - A new Order instance is created with the customer, selected address, delivery status, payment method, and the
      total cost.
    - Stripe PaymentIntent is also created for the total cost of the order and PaymentIntent's id is
      saved with the order.

4. Converting Cart Items Order Items:
    - For each item in the cart, an OrderItem instance is created. These OrderItem instances link the Order with
      the products the customer purchased, along with their product variants, quantities, and total prices.
    - All OrderItem instances are created in bulk using bulk_create for efficiency.

5. Trigger Order Created Signal:
    - A signal (order_created.send_robust) is sent after the order is successfully created,
      which could be used to trigger other actions (e.g. sending confirmation emails, notifying warehouse, etc.).