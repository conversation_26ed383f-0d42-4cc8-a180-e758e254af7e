# API Reference: Decoupled Cart and Shipping

## Overview

This document provides detailed API reference for the new decoupled cart and shipping architecture.

## Cart Operations (Fast Path)

### Add Item to Cart
```http
POST /api/cart/{cart_id}/items/
```

**Request Body:**
```json
{
  "product_id": 123,
  "product_variant": 456,
  "quantity": 2,
  "extra_data": {}
}
```

**Response (200 OK):**
```json
{
  "id": 789,
  "product_id": 123,
  "product_variant": 456,
  "quantity": 2,
  "extra_data": {}
}
```

**Performance:** ~50-200ms (no shipping calculation)

### Update Item Quantity
```http
PATCH /api/cart/{cart_id}/items/{item_id}/
```

**Request Body:**
```json
{
  "quantity": 3
}
```

**Response (200 OK):**
```json
{
  "quantity": 3
}
```

### Get Cart
```http
GET /api/cart/{cart_id}/
```

**Response (200 OK):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "cart_items": [
    {
      "id": 789,
      "product": {
        "id": 123,
        "title": "Gaming Mouse",
        "slug": "gaming-mouse"
      },
      "product_variant": {
        "id": 456,
        "sku": "GM-001-BLK",
        "price": "59.99"
      },
      "quantity": 2,
      "qty_price": "119.98"
    }
  ],
  "customer": null,
  "cart_weight": 340.0,
  "total_price": "119.98",
  "item_count": 2,
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Note:** No shipping-related fields (shipping_cost, packing_cost, grand_total)

## Shipping Calculations (On-Demand)

### Calculate Shipping
```http
POST /api/cart/{cart_id}/items/calculate_shipping/
```

**Request Body:**
```json
{
  "destination_address_id": 123,
  "get_all_options": false
}
```

**Alternative with manual address:**
```json
{
  "destination_address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "country": "US"
  },
  "get_all_options": true
}
```

**Response (200 OK):**
```json
{
  "cart": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "cart_items": [...],
    "total_price": "119.98",
    "shipping_cost": "15.50",
    "packing_cost": "2.00",
    "grand_total": "137.48",
    "total_weight": 340.0,
    "total_volume": "125.5000",
    "last_shipping_calculation": "2024-01-15T10:35:00Z"
  },
  "shipping_calculation": {
    "success": true,
    "message": "Shipping calculated successfully",
    "calculation_time": 2.345,
    "packing_cost": 2.00,
    "shipping_cost": 15.50,
    "total_weight": 340.0,
    "total_volume": "125.5000",
    "shipping_options": [
      {
        "carrier_name": "FedEx",
        "service_name": "Ground",
        "cost": 15.50,
        "estimated_days": 3,
        "max_days": 5,
        "tracking_available": true,
        "insurance_available": true,
        "signature_available": false
      },
      {
        "carrier_name": "UPS",
        "service_name": "Ground",
        "cost": 16.25,
        "estimated_days": 3,
        "max_days": 5,
        "tracking_available": true,
        "insurance_available": true,
        "signature_available": false
      }
    ],
    "packing_details": {
      "boxes_count": 1,
      "method_used": "3d_bin_packing",
      "calculation_time": 1.234,
      "warnings": []
    }
  }
}
```

**Performance:** ~2-5s (includes 3D packing and carrier APIs)

### Get Shipping Estimate
```http
GET /api/cart/{cart_id}/items/shipping_estimate/?zone=domestic
```

**Query Parameters:**
- `zone`: "domestic" or "international" (default: "domestic")

**Response (200 OK):**
```json
{
  "success": true,
  "estimated_cost": 15.00,
  "weight": 340.0,
  "note": "This is a rough estimate. Calculate exact shipping before checkout."
}
```

**Performance:** ~10-50ms (simple weight-based calculation)

## Error Responses

### Validation Errors (400 Bad Request)
```json
{
  "error": "Validation failed",
  "details": {
    "quantity": ["Quantity must be at least 1."],
    "weight": ["Adding this item would exceed the maximum weight limit of 20,000 grams."]
  }
}
```

### Cart Not Found (404 Not Found)
```json
{
  "error": "Cart not found"
}
```

### Shipping Calculation Failed (500 Internal Server Error)
```json
{
  "error": "Failed to calculate shipping",
  "details": "Packing calculation failed: No suitable boxes found"
}
```

## Request/Response Examples

### Complete User Flow

#### 1. Create Cart and Add Items
```bash
# Add first item
curl -X POST /api/cart/550e8400-e29b-41d4-a716-446655440000/items/ \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 123,
    "product_variant": 456,
    "quantity": 1
  }'

# Add second item
curl -X POST /api/cart/550e8400-e29b-41d4-a716-446655440000/items/ \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 124,
    "product_variant": 457,
    "quantity": 2
  }'
```

#### 2. Get Cart (Fast - No Shipping)
```bash
curl -X GET /api/cart/550e8400-e29b-41d4-a716-446655440000/
```

#### 3. Get Quick Estimate (Optional)
```bash
curl -X GET /api/cart/550e8400-e29b-41d4-a716-446655440000/items/shipping_estimate/?zone=domestic
```

#### 4. Calculate Exact Shipping (Before Checkout)
```bash
curl -X POST /api/cart/550e8400-e29b-41d4-a716-446655440000/items/calculate_shipping/ \
  -H "Content-Type: application/json" \
  -d '{
    "destination_address_id": 123,
    "get_all_options": true
  }'
```

## Frontend Integration

### React Example
```javascript
// Fast cart operations
const addToCart = async (productId, variantId, quantity) => {
  const response = await fetch(`/api/cart/${cartId}/items/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      product_id: productId,
      product_variant: variantId,
      quantity: quantity
    })
  });
  
  // Fast response - update UI immediately
  const item = await response.json();
  updateCartUI(item);
};

// On-demand shipping calculation
const calculateShipping = async (addressId) => {
  setShippingLoading(true);
  
  try {
    const response = await fetch(`/api/cart/${cartId}/items/calculate_shipping/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        destination_address_id: addressId,
        get_all_options: true
      })
    });
    
    const result = await response.json();
    updateCartWithShipping(result.cart);
    showShippingOptions(result.shipping_calculation.shipping_options);
  } finally {
    setShippingLoading(false);
  }
};
```

## Performance Characteristics

### Cart Operations
- **Response Time**: 50-200ms
- **Database Queries**: 2-4 queries
- **CPU Usage**: Low
- **Memory Usage**: Low
- **Scalability**: High (can handle 1000+ concurrent operations)

### Shipping Calculations
- **Response Time**: 2-5s
- **Database Queries**: 10-20 queries
- **CPU Usage**: High (3D bin packing)
- **Memory Usage**: Medium (2-3MB per calculation)
- **Scalability**: Medium (limited by CPU-intensive calculations)

## Rate Limiting

### Cart Operations
- **Rate Limit**: 100 requests/minute per user
- **Burst Limit**: 20 requests/10 seconds

### Shipping Calculations
- **Rate Limit**: 10 requests/minute per user
- **Burst Limit**: 3 requests/10 seconds

## Caching

### Cart Data
- **TTL**: 1 hour
- **Cache Key**: `cart:{cart_id}`
- **Invalidation**: On cart modification

### Shipping Calculations
- **TTL**: 30 minutes
- **Cache Key**: `shipping:{cart_signature}:{address_hash}`
- **Invalidation**: On cart content change

## Migration Notes

### Breaking Changes
1. **Cart response format**: Removed shipping-related fields from default cart response
2. **New endpoints**: Added shipping calculation endpoints
3. **Response times**: Cart operations now much faster, shipping calculations separate

### Backward Compatibility
- Old shipping recalculation endpoint deprecated but still functional
- Gradual migration recommended
- Feature flags available for rollback
