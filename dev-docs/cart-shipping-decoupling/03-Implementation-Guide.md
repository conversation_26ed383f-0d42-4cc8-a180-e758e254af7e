# Implementation Guide: Cart-Shipping Decoupling

## Overview

This guide provides step-by-step instructions for implementing and deploying the new decoupled cart-shipping architecture.

## Code Changes Summary

### 1. Cart Serializers (`apps/cart/serializers.py`)

#### New CartSerializer (Fast)
```python
class CartSerializer(ModelSerializer):
    """Serializer for cart without shipping calculations - shows only item totals"""
    id = serializers.UUIDField(read_only=True)
    cart_items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.SerializerMethodField()
    cart_weight = serializers.SerializerMethodField()
    item_count = serializers.SerializerMethodField()
    
    # No shipping-related fields
```

#### New CartWithShippingSerializer
```python
class CartWithShippingSerializer(ModelSerializer):
    """Serializer for cart with shipping calculations - used after shipping calculation"""
    # Includes all fields: shipping_cost, packing_cost, grand_total, etc.
```

#### Updated AddCartItemSerializer
```python
def save(self, **kwargs):
    # ... cart item creation logic ...
    # REMOVED: self._recalculate_shipping(cart)
    # Note: Shipping calculation is now decoupled and done on-demand
```

### 2. Cart Views (`apps/cart/views.py`)

#### New Shipping Calculation Endpoint
```python
@action(detail=False, methods=['post'])
def calculate_shipping(self, request, cart_pk=None):
    """Calculate shipping costs for cart on-demand"""
    # Full implementation with address handling and shipping options
```

#### New Shipping Estimate Endpoint
```python
@action(detail=False, methods=['get'])
def shipping_estimate(self, request, cart_pk=None):
    """Get a quick shipping estimate for the cart"""
    # Simple weight-based estimation
```

### 3. On-Demand Shipping Service (`apps/shipping/services/on_demand.py`)

#### New OnDemandShippingService
```python
class OnDemandShippingService:
    """Service for on-demand shipping calculations"""
    
    def calculate_shipping_for_cart(self, cart, destination_address, get_all_options=False):
        """Calculate shipping with detailed options"""
        
    def get_shipping_estimate(self, cart, destination_zone='domestic'):
        """Get quick estimate without full calculation"""
```

## Deployment Steps

### Phase 1: Code Deployment (Zero Downtime)

1. **Deploy New Code**
   ```bash
   # Deploy with feature flag disabled
   git checkout feature/cart-shipping-decoupling
   python manage.py migrate  # No migrations needed
   python manage.py collectstatic
   # Deploy to production
   ```

2. **Verify Backward Compatibility**
   ```bash
   # Test existing endpoints still work
   curl -X GET /api/cart/{cart_id}/
   curl -X POST /api/cart/{cart_id}/items/
   ```

### Phase 2: Feature Activation

1. **Enable New Endpoints**
   ```python
   # In settings.py or feature flags
   CART_SHIPPING_DECOUPLED = True
   ```

2. **Monitor Performance**
   ```bash
   # Check response times
   tail -f /var/log/nginx/access.log | grep "cart"
   
   # Monitor error rates
   tail -f /var/log/app/django.log | grep "ERROR"
   ```

### Phase 3: Frontend Migration

1. **Update Frontend Code**
   ```javascript
   // Remove shipping fields from cart display
   const CartSummary = ({ cart }) => (
     <div>
       <div>Items: {cart.total_price}</div>
       <div>Weight: {cart.cart_weight}g</div>
       {/* Removed: shipping_cost, grand_total */}
     </div>
   );
   
   // Add shipping calculation button
   const ShippingCalculator = ({ cartId }) => (
     <button onClick={() => calculateShipping(cartId)}>
       Calculate Shipping
     </button>
   );
   ```

2. **Update Checkout Flow**
   ```javascript
   const CheckoutFlow = () => {
     const [shippingCalculated, setShippingCalculated] = useState(false);
     
     const handleCalculateShipping = async () => {
       const result = await calculateShipping(cartId, addressId);
       setShippingCalculated(true);
       setShippingOptions(result.shipping_options);
     };
     
     return (
       <div>
         {!shippingCalculated && (
           <ShippingCalculator onCalculate={handleCalculateShipping} />
         )}
         {shippingCalculated && (
           <ShippingOptions options={shippingOptions} />
         )}
       </div>
     );
   };
   ```

## Testing Strategy

### 1. Unit Tests

#### Cart Operations Tests
```python
class TestCartOperations(TestCase):
    def test_add_item_fast_response(self):
        """Test that adding items is fast and doesn't trigger shipping"""
        start_time = time.time()
        response = self.client.post(f'/api/cart/{self.cart.id}/items/', {
            'product_id': self.product.id,
            'product_variant': self.variant.id,
            'quantity': 1
        })
        response_time = time.time() - start_time
        
        self.assertEqual(response.status_code, 201)
        self.assertLess(response_time, 0.5)  # Should be under 500ms
        
    def test_cart_response_excludes_shipping(self):
        """Test that cart response doesn't include shipping fields"""
        response = self.client.get(f'/api/cart/{self.cart.id}/')
        data = response.json()
        
        self.assertIn('total_price', data)
        self.assertIn('cart_weight', data)
        self.assertNotIn('shipping_cost', data)
        self.assertNotIn('grand_total', data)
```

#### Shipping Calculation Tests
```python
class TestShippingCalculation(TestCase):
    def test_calculate_shipping_endpoint(self):
        """Test shipping calculation endpoint"""
        response = self.client.post(
            f'/api/cart/{self.cart.id}/items/calculate_shipping/',
            {
                'destination_address_id': self.address.id,
                'get_all_options': True
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('shipping_calculation', data)
        self.assertIn('shipping_options', data['shipping_calculation'])
        
    def test_shipping_estimate_endpoint(self):
        """Test quick shipping estimate"""
        response = self.client.get(
            f'/api/cart/{self.cart.id}/items/shipping_estimate/?zone=domestic'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('estimated_cost', data)
        self.assertIn('weight', data)
```

### 2. Performance Tests

#### Load Testing Script
```python
import asyncio
import aiohttp
import time

async def test_cart_performance():
    """Test cart operation performance under load"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(100):  # 100 concurrent requests
            task = session.post(f'/api/cart/{cart_id}/items/', json={
                'product_id': 123,
                'product_variant': 456,
                'quantity': 1
            })
            tasks.append(task)
        
        start_time = time.time()
        responses = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"100 requests completed in {total_time:.2f}s")
        print(f"Average response time: {total_time/100:.3f}s")
        
        # Verify all requests succeeded
        for response in responses:
            assert response.status == 201

# Run the test
asyncio.run(test_cart_performance())
```

### 3. Integration Tests

#### End-to-End User Flow
```python
class TestUserFlow(TestCase):
    def test_complete_shopping_flow(self):
        """Test complete user flow from cart to checkout"""
        
        # 1. Add items to cart (should be fast)
        start_time = time.time()
        for i in range(3):
            response = self.client.post(f'/api/cart/{self.cart.id}/items/', {
                'product_id': self.products[i].id,
                'product_variant': self.variants[i].id,
                'quantity': 1
            })
            self.assertEqual(response.status_code, 201)
        
        cart_operations_time = time.time() - start_time
        self.assertLess(cart_operations_time, 2.0)  # All operations under 2s
        
        # 2. Get cart (should not include shipping)
        response = self.client.get(f'/api/cart/{self.cart.id}/')
        cart_data = response.json()
        self.assertNotIn('shipping_cost', cart_data)
        
        # 3. Calculate shipping (can be slower)
        start_time = time.time()
        response = self.client.post(
            f'/api/cart/{self.cart.id}/items/calculate_shipping/',
            {'destination_address_id': self.address.id}
        )
        shipping_time = time.time() - start_time
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(shipping_time, 10.0)  # Under 10s acceptable
        
        shipping_data = response.json()
        self.assertIn('shipping_cost', shipping_data['cart'])
        self.assertIn('grand_total', shipping_data['cart'])
```

## Monitoring and Alerting

### 1. Performance Metrics

#### Application Metrics
```python
# In Django middleware or views
import time
from django.core.cache import cache

class PerformanceMonitoringMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        response = self.get_response(request)
        response_time = time.time() - start_time
        
        # Track cart operation performance
        if '/api/cart/' in request.path and '/items/' in request.path:
            if 'calculate_shipping' in request.path:
                # Shipping calculation - expect 2-5s
                cache.set(f'shipping_calc_time_{int(time.time())}', response_time, 300)
            else:
                # Cart operation - expect < 200ms
                cache.set(f'cart_op_time_{int(time.time())}', response_time, 300)
                
                if response_time > 0.5:  # Alert if over 500ms
                    logger.warning(f"Slow cart operation: {response_time:.3f}s")
        
        return response
```

#### Database Query Monitoring
```python
# In settings.py for development
if DEBUG:
    LOGGING['loggers']['django.db.backends'] = {
        'level': 'DEBUG',
        'handlers': ['console'],
    }

# Monitor query count in tests
from django.test.utils import override_settings
from django.db import connection

@override_settings(DEBUG=True)
def test_cart_query_efficiency(self):
    """Ensure cart operations use minimal queries"""
    with self.assertNumQueries(3):  # Should be 3 or fewer queries
        response = self.client.post(f'/api/cart/{self.cart.id}/items/', {
            'product_id': self.product.id,
            'product_variant': self.variant.id,
            'quantity': 1
        })
```

### 2. Error Monitoring

#### Custom Error Tracking
```python
import logging

logger = logging.getLogger(__name__)

class ShippingCalculationError(Exception):
    """Custom exception for shipping calculation failures"""
    pass

# In OnDemandShippingService
def calculate_shipping_for_cart(self, cart, destination_address, get_all_options=False):
    try:
        # ... calculation logic ...
        return result
    except Exception as e:
        logger.error(f"Shipping calculation failed for cart {cart.id}: {e}", extra={
            'cart_id': str(cart.id),
            'item_count': cart.cart_items.count(),
            'total_weight': cart.get_cart_weight(),
            'destination': str(destination_address)
        })
        raise ShippingCalculationError(f"Failed to calculate shipping: {e}")
```

### 3. Business Metrics

#### Cart Abandonment Tracking
```python
# Track cart abandonment at different stages
class CartAnalytics:
    @staticmethod
    def track_cart_event(cart_id, event_type, metadata=None):
        """Track cart events for analytics"""
        from apps.analytics.models import CartEvent
        
        CartEvent.objects.create(
            cart_id=cart_id,
            event_type=event_type,  # 'item_added', 'shipping_calculated', 'checkout_started'
            metadata=metadata or {},
            timestamp=timezone.now()
        )
    
    @staticmethod
    def get_abandonment_funnel():
        """Get cart abandonment funnel data"""
        from django.db.models import Count
        
        return CartEvent.objects.values('event_type').annotate(
            count=Count('cart_id', distinct=True)
        ).order_by('timestamp')

# Usage in views
def add_cart_item(request, cart_pk):
    # ... add item logic ...
    CartAnalytics.track_cart_event(cart_pk, 'item_added', {
        'product_id': product_id,
        'quantity': quantity
    })

def calculate_shipping(request, cart_pk):
    # ... shipping calculation ...
    CartAnalytics.track_cart_event(cart_pk, 'shipping_calculated', {
        'shipping_cost': float(result['shipping_cost']),
        'calculation_time': result['calculation_time']
    })
```

## Rollback Plan

### 1. Feature Flag Rollback
```python
# In settings.py
CART_SHIPPING_DECOUPLED = False  # Disable new behavior

# In views.py
if settings.CART_SHIPPING_DECOUPLED:
    # Use new decoupled approach
    return new_calculate_shipping(request, cart_pk)
else:
    # Use old coupled approach
    return old_recalculate_shipping(request, cart_pk)
```

### 2. Database Rollback
```python
# No database migrations needed - rollback is code-only
# Cart model fields remain unchanged
# Shipping calculation data preserved
```

### 3. Frontend Rollback
```javascript
// Revert to showing shipping costs in cart
const CartSummary = ({ cart }) => (
  <div>
    <div>Items: {cart.total_price}</div>
    <div>Shipping: {cart.shipping_cost || 'Calculating...'}</div>
    <div>Total: {cart.grand_total || cart.total_price}</div>
  </div>
);
```

## Success Criteria

### Performance Targets
- ✅ Cart operations: < 200ms average response time
- ✅ Shipping calculations: < 5s average response time
- ✅ 99th percentile cart operations: < 500ms
- ✅ Error rate: < 1% for cart operations, < 5% for shipping calculations

### Business Metrics
- 📊 Cart abandonment rate during shopping: Maintain or improve
- 📊 Conversion rate: Maintain or improve
- 📊 User satisfaction: Monitor through surveys and feedback
- 📊 Support tickets: Monitor for shipping-related confusion

### Technical Metrics
- 🔧 Server CPU usage: Reduce by 30-50% during peak hours
- 🔧 Database query count: Reduce by 60-80% for cart operations
- 🔧 Concurrent user capacity: Increase by 5-10x
- 🔧 Infrastructure costs: Reduce by 20-30%
