# Monitoring Strategy Implementation Guide

## Overview

Comprehensive monitoring is critical for maintaining performance and reliability of the optimized cart system. This guide implements observability across all layers: application metrics, infrastructure monitoring, business metrics, and alerting strategies.

## Monitoring Architecture

### Observability Stack

```mermaid
graph TB
    A[Application Services] --> B[Metrics Collection]
    A --> C[Log Aggregation]
    A --> D[Distributed Tracing]
    
    B --> E[Prometheus]
    C --> F[ELK Stack]
    D --> G[<PERSON><PERSON><PERSON>]
    
    E --> H[Grafana Dashboards]
    F --> H
    G --> H
    
    E --> I[AlertManager]
    I --> J[PagerDuty/Slack]
    
    K[Business Intelligence] --> L[Custom Dashboards]
    A --> K
```

### Monitoring Layers

1. **Infrastructure Monitoring**: CPU, memory, disk, network
2. **Application Monitoring**: Response times, error rates, throughput
3. **Business Monitoring**: Cart conversion rates, revenue impact
4. **User Experience Monitoring**: Real user metrics, performance perception

## Implementation Guide

### Phase 1: Application Metrics (Day 1-2)

#### Step 1: Prometheus Integration

**1.1 Install Prometheus Dependencies**
```bash
pip install prometheus-client==0.19.0
pip install django-prometheus==2.3.1
```

**1.2 Django Prometheus Configuration**
```python
# pc_hardware/settings/common.py
INSTALLED_APPS += [
    'django_prometheus',
]

MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',
    # ... existing middleware ...
    'django_prometheus.middleware.PrometheusAfterMiddleware',
]

# Prometheus metrics configuration
PROMETHEUS_EXPORT_MIGRATIONS = False
```

**1.3 Custom Cart Metrics**
```python
# apps/cart/metrics.py
from prometheus_client import Counter, Histogram, Gauge, Summary
import time

# Cart operation metrics
cart_operations_total = Counter(
    'cart_operations_total',
    'Total cart operations',
    ['operation_type', 'status']
)

cart_operation_duration = Histogram(
    'cart_operation_duration_seconds',
    'Cart operation duration',
    ['operation_type'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

cart_items_gauge = Gauge(
    'cart_items_current',
    'Current number of items in carts',
    ['cart_status']
)

shipping_calculation_duration = Summary(
    'shipping_calculation_duration_seconds',
    'Shipping calculation duration'
)

cache_operations = Counter(
    'cache_operations_total',
    'Cache operations',
    ['cache_type', 'operation', 'result']
)

database_queries = Counter(
    'database_queries_total',
    'Database queries',
    ['model', 'operation']
)

class CartMetricsMiddleware:
    """Middleware to collect cart-specific metrics"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Collect metrics for cart operations
        if '/api/cart/' in request.path:
            duration = time.time() - start_time
            operation_type = self.get_operation_type(request)
            status = 'success' if response.status_code < 400 else 'error'
            
            cart_operations_total.labels(
                operation_type=operation_type,
                status=status
            ).inc()
            
            cart_operation_duration.labels(
                operation_type=operation_type
            ).observe(duration)
        
        return response
    
    def get_operation_type(self, request):
        """Determine operation type from request"""
        if 'items' in request.path:
            if request.method == 'POST':
                return 'add_item'
            elif request.method == 'PATCH':
                return 'update_item'
            elif request.method == 'DELETE':
                return 'remove_item'
        elif request.method == 'GET':
            return 'get_cart'
        elif request.method == 'POST':
            return 'create_cart'
        return 'unknown'
```

**1.4 Shipping Service Metrics**
```python
# apps/shipping/metrics.py
from prometheus_client import Counter, Histogram, Gauge

shipping_calculations_total = Counter(
    'shipping_calculations_total',
    'Total shipping calculations',
    ['status', 'carrier']
)

shipping_calculation_duration = Histogram(
    'shipping_calculation_duration_seconds',
    'Shipping calculation duration',
    ['calculation_type'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0]
)

carrier_api_calls = Counter(
    'carrier_api_calls_total',
    'Carrier API calls',
    ['carrier', 'status']
)

carrier_response_time = Histogram(
    'carrier_response_time_seconds',
    'Carrier API response time',
    ['carrier'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
)

packing_calculations = Counter(
    'packing_calculations_total',
    'Packing calculations',
    ['algorithm', 'status']
)

cache_hit_rate = Gauge(
    'cache_hit_rate',
    'Cache hit rate percentage',
    ['cache_type']
)
```

#### Step 2: Business Metrics

**2.1 Cart Business Metrics**
```python
# apps/cart/business_metrics.py
from prometheus_client import Counter, Gauge, Histogram
from decimal import Decimal

# Business metrics
cart_value_histogram = Histogram(
    'cart_value_dollars',
    'Cart value distribution',
    buckets=[10, 25, 50, 100, 250, 500, 1000, 2500]
)

cart_abandonment_rate = Gauge(
    'cart_abandonment_rate',
    'Cart abandonment rate percentage'
)

average_cart_items = Gauge(
    'average_cart_items',
    'Average number of items per cart'
)

conversion_rate = Gauge(
    'cart_conversion_rate',
    'Cart to order conversion rate'
)

revenue_impact = Counter(
    'revenue_impact_dollars',
    'Revenue impact from cart operations',
    ['operation_type']
)

class BusinessMetricsCollector:
    """Collect business metrics for carts"""
    
    def __init__(self):
        self.update_interval = 300  # 5 minutes
    
    def collect_cart_metrics(self):
        """Collect current cart metrics"""
        from .models import Cart, CartItem
        from django.db.models import Avg, Count, Sum
        from django.utils import timezone
        from datetime import timedelta
        
        # Active carts in last 24 hours
        yesterday = timezone.now() - timedelta(days=1)
        active_carts = Cart.objects.filter(
            cart_items__isnull=False,
            cart_items__updated_at__gte=yesterday
        ).distinct()
        
        # Average cart value
        cart_values = []
        for cart in active_carts:
            total_value = sum(
                item.get_total_item_price() 
                for item in cart.cart_items.all()
            )
            cart_values.append(float(total_value))
            cart_value_histogram.observe(float(total_value))
        
        # Average items per cart
        avg_items = active_carts.aggregate(
            avg_items=Avg('cart_items__quantity')
        )['avg_items'] or 0
        average_cart_items.set(avg_items)
        
        # Cart abandonment rate (carts not converted to orders in 24h)
        from apps.order.models import Order
        converted_carts = Order.objects.filter(
            placed_at__gte=yesterday
        ).values_list('cart_id', flat=True)
        
        abandonment_rate = (
            (active_carts.count() - len(converted_carts)) / 
            max(active_carts.count(), 1) * 100
        )
        cart_abandonment_rate.set(abandonment_rate)
    
    def track_cart_operation_revenue(self, operation_type, cart_value):
        """Track revenue impact of cart operations"""
        revenue_impact.labels(operation_type=operation_type).inc(cart_value)
```

### Phase 2: Infrastructure Monitoring (Day 3-4)

#### Step 3: Prometheus Server Setup

**3.1 Prometheus Configuration**
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "cart_rules.yml"
  - "shipping_rules.yml"

scrape_configs:
  - job_name: 'django-cart'
    static_configs:
      - targets: ['cart-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'django-shipping'
    static_configs:
      - targets: ['shipping-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

**3.2 Alert Rules**
```yaml
# monitoring/prometheus/cart_rules.yml
groups:
  - name: cart_performance
    rules:
      - alert: HighCartResponseTime
        expr: histogram_quantile(0.95, cart_operation_duration_seconds_bucket) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High cart operation response time"
          description: "95th percentile response time is {{ $value }}s"

      - alert: HighCartErrorRate
        expr: rate(cart_operations_total{status="error"}[5m]) / rate(cart_operations_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High cart error rate"
          description: "Cart error rate is {{ $value | humanizePercentage }}"

      - alert: LowCacheHitRate
        expr: cache_hit_rate < 70
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value }}%"

  - name: business_metrics
    rules:
      - alert: HighCartAbandonmentRate
        expr: cart_abandonment_rate > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High cart abandonment rate"
          description: "Cart abandonment rate is {{ $value }}%"

      - alert: LowConversionRate
        expr: cart_conversion_rate < 5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low cart conversion rate"
          description: "Conversion rate is {{ $value }}%"
```

#### Step 4: Grafana Dashboards

**4.1 Cart Performance Dashboard**
```json
{
  "dashboard": {
    "title": "Cart Performance Dashboard",
    "panels": [
      {
        "title": "Cart Operations Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(cart_operations_total[5m])",
            "legendFormat": "{{operation_type}} - {{status}}"
          }
        ]
      },
      {
        "title": "Response Time Percentiles",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, cart_operation_duration_seconds_bucket)",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, cart_operation_duration_seconds_bucket)",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.99, cart_operation_duration_seconds_bucket)",
            "legendFormat": "99th percentile"
          }
        ]
      },
      {
        "title": "Cache Hit Rates",
        "type": "singlestat",
        "targets": [
          {
            "expr": "cache_hit_rate",
            "legendFormat": "{{cache_type}}"
          }
        ]
      },
      {
        "title": "Database Query Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(database_queries_total[5m])",
            "legendFormat": "{{model}} - {{operation}}"
          }
        ]
      }
    ]
  }
}
```

**4.2 Business Metrics Dashboard**
```json
{
  "dashboard": {
    "title": "Cart Business Metrics",
    "panels": [
      {
        "title": "Cart Value Distribution",
        "type": "histogram",
        "targets": [
          {
            "expr": "cart_value_dollars_bucket",
            "legendFormat": "Cart Value"
          }
        ]
      },
      {
        "title": "Conversion Funnel",
        "type": "table",
        "targets": [
          {
            "expr": "cart_operations_total{operation_type='add_item'}",
            "legendFormat": "Items Added"
          },
          {
            "expr": "cart_operations_total{operation_type='create_cart'}",
            "legendFormat": "Carts Created"
          }
        ]
      },
      {
        "title": "Revenue Impact",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(revenue_impact_dollars[1h])",
            "legendFormat": "{{operation_type}}"
          }
        ]
      }
    ]
  }
}
```

### Phase 3: Log Aggregation (Day 5-6)

#### Step 5: ELK Stack Setup

**5.1 Structured Logging Configuration**
```python
# pc_hardware/settings/logging.py
import os

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/cart.log',
            'maxBytes': 1024*1024*100,  # 100MB
            'backupCount': 5,
            'formatter': 'json',
        },
        'elasticsearch': {
            'class': 'elasticsearch_logging.ElasticsearchHandler',
            'hosts': [{'host': 'elasticsearch', 'port': 9200}],
            'index': 'django-logs',
            'formatter': 'json',
        },
    },
    'loggers': {
        'apps.cart': {
            'handlers': ['console', 'file', 'elasticsearch'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps.shipping': {
            'handlers': ['console', 'file', 'elasticsearch'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
}
```

**5.2 Custom Log Middleware**
```python
# apps/core/middleware/logging.py
import logging
import time
import uuid
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(MiddlewareMixin):
    """Log all requests with correlation IDs"""
    
    def process_request(self, request):
        request.correlation_id = str(uuid.uuid4())
        request.start_time = time.time()
        
        logger.info("Request started", extra={
            'correlation_id': request.correlation_id,
            'method': request.method,
            'path': request.path,
            'user_id': request.user.id if request.user.is_authenticated else None,
            'ip_address': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        })
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info("Request completed", extra={
                'correlation_id': getattr(request, 'correlation_id', 'unknown'),
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'response_size': len(response.content) if hasattr(response, 'content') else 0,
            })
        
        return response
    
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

#### Step 6: Distributed Tracing

**6.1 Jaeger Integration**
```python
# apps/core/tracing.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.django import DjangoInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.psycopg2 import Psycopg2Instrumentor

def setup_tracing():
    """Setup distributed tracing with Jaeger"""
    trace.set_tracer_provider(TracerProvider())
    tracer = trace.get_tracer(__name__)
    
    jaeger_exporter = JaegerExporter(
        agent_host_name="jaeger",
        agent_port=6831,
    )
    
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Auto-instrument Django, Redis, and PostgreSQL
    DjangoInstrumentor().instrument()
    RedisInstrumentor().instrument()
    Psycopg2Instrumentor().instrument()

# Custom tracing for cart operations
def trace_cart_operation(operation_name):
    """Decorator to trace cart operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracer = trace.get_tracer(__name__)
            with tracer.start_as_current_span(operation_name) as span:
                span.set_attribute("operation.name", operation_name)
                span.set_attribute("operation.args", str(args))
                
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("operation.success", True)
                    return result
                except Exception as e:
                    span.set_attribute("operation.success", False)
                    span.set_attribute("operation.error", str(e))
                    raise
        return wrapper
    return decorator
```

## Alerting Strategy

### Alert Configuration

**AlertManager Configuration:**
```yaml
# monitoring/alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'pagerduty'
  - match:
      severity: warning
    receiver: 'slack'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://webhook-service:5000/alerts'

- name: 'pagerduty'
  pagerduty_configs:
  - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
    description: 'Critical alert: {{ .GroupLabels.alertname }}'

- name: 'slack'
  slack_configs:
  - api_url: 'YOUR_SLACK_WEBHOOK_URL'
    channel: '#alerts'
    title: 'Warning: {{ .GroupLabels.alertname }}'
    text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
```

### SLA Monitoring

**Service Level Objectives:**
```python
# apps/core/monitoring/slo.py
from prometheus_client import Counter, Histogram

# SLI metrics
request_duration_sli = Histogram(
    'http_request_duration_seconds_sli',
    'Request duration for SLI calculation',
    ['service', 'endpoint'],
    buckets=[0.1, 0.2, 0.5, 1.0, 2.0, 5.0]
)

error_rate_sli = Counter(
    'http_requests_total_sli',
    'Total requests for SLI calculation',
    ['service', 'endpoint', 'status']
)

# SLO targets
SLO_TARGETS = {
    'cart_service': {
        'availability': 99.9,  # 99.9% uptime
        'latency_p95': 200,    # 95% of requests < 200ms
        'error_rate': 0.1,     # < 0.1% error rate
    },
    'shipping_service': {
        'availability': 99.5,  # 99.5% uptime
        'latency_p95': 500,    # 95% of requests < 500ms
        'error_rate': 0.5,     # < 0.5% error rate
    }
}

class SLOMonitor:
    """Monitor Service Level Objectives"""
    
    def calculate_availability(self, service, time_window='1h'):
        """Calculate service availability"""
        # Implementation for availability calculation
        pass
    
    def calculate_latency_percentile(self, service, percentile=95, time_window='1h'):
        """Calculate latency percentile"""
        # Implementation for latency calculation
        pass
    
    def calculate_error_rate(self, service, time_window='1h'):
        """Calculate error rate"""
        # Implementation for error rate calculation
        pass
```

## Expected Monitoring Benefits

### Operational Improvements

1. **Faster Issue Detection**: Alerts within 1-2 minutes of problems
2. **Better Root Cause Analysis**: Distributed tracing shows request flow
3. **Proactive Optimization**: Identify performance bottlenecks before they impact users
4. **Capacity Planning**: Historical data for scaling decisions

### Business Benefits

1. **Revenue Protection**: Quick detection of issues affecting sales
2. **Customer Experience**: Monitor real user performance
3. **Cost Optimization**: Identify over-provisioned resources
4. **Compliance**: SLA monitoring and reporting

## Next Steps

1. **Deploy monitoring stack** in staging environment
2. **Configure alerts** based on baseline metrics
3. **Train operations team** on new dashboards and procedures
4. **Implement automated remediation** for common issues
5. **Establish SLA reporting** for business stakeholders

This comprehensive monitoring strategy provides visibility into all aspects of the cart system, enabling proactive management and continuous optimization.
