# Microservices Architecture Guide

## Overview

This guide outlines the transition from a monolithic cart system to a microservices architecture that can scale to handle 10,000+ concurrent users. The microservices approach provides better fault isolation, independent scaling, and improved maintainability.

## Current Monolithic Issues

### Tight Coupling Problems
- Cart, shipping, and packing logic tightly coupled
- Single point of failure for all cart operations
- Difficult to scale individual components
- Complex deployment dependencies

### Scaling Limitations
- Cannot scale cart operations independently from shipping calculations
- Resource contention between different operations
- Difficult to optimize for specific workloads

## Proposed Microservices Architecture

### Service Decomposition

```mermaid
graph TB
    A[API Gateway] --> B[Cart Service]
    A --> C[Shipping Service]
    A --> D[Packing Service]
    A --> E[Product Service]
    A --> F[Inventory Service]
    
    B --> G[Cart Database]
    C --> H[Shipping Database]
    D --> I[Packing Database]
    E --> J[Product Database]
    F --> K[Inventory Database]
    
    B --> L[Message Bus]
    C --> L
    D --> L
    E --> L
    F --> L
    
    L --> M[Event Store]
    
    N[Redis Cache] --> B
    N --> C
    N --> D
```

### Service Boundaries

**Cart Service:**
- Cart CRUD operations
- Cart item management
- Cart validation
- User session management

**Shipping Service:**
- Shipping rate calculations
- Carrier integrations
- Shipping method selection
- Address validation

**Packing Service:**
- 3D bin packing algorithms
- Box optimization
- Packing rules engine
- Volume calculations

**Product Service:**
- Product catalog
- Product variants
- Pricing information
- Product availability

**Inventory Service:**
- Stock management
- Inventory reservations
- Stock level monitoring
- Reorder notifications

## Implementation Strategy

### Phase 1: Service Extraction (Week 1-2)

#### Step 1: Extract Shipping Service

**1.1 Create Shipping Service Structure**
```bash
mkdir services/shipping-service
cd services/shipping-service

# Create Django project for shipping service
django-admin startproject shipping_service .
cd shipping_service
python manage.py startapp shipping
python manage.py startapp carriers
```

**1.2 Shipping Service Models**
```python
# services/shipping-service/shipping/models.py
from django.db import models
from decimal import Decimal

class ShippingCalculation(models.Model):
    """Shipping calculation request and result"""
    calculation_id = models.UUIDField(primary_key=True, default=uuid4)
    cart_signature = models.CharField(max_length=64, db_index=True)
    destination_country = models.CharField(max_length=2)
    destination_postal_code = models.CharField(max_length=20)
    
    # Request data
    total_weight = models.DecimalField(max_digits=8, decimal_places=2)
    total_volume = models.DecimalField(max_digits=12, decimal_places=4)
    item_count = models.PositiveIntegerField()
    
    # Results
    shipping_cost = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    estimated_days = models.PositiveIntegerField(null=True)
    carrier_name = models.CharField(max_length=100, null=True)
    service_name = models.CharField(max_length=100, null=True)
    
    # Metadata
    status = models.CharField(max_length=20, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True)
    error_message = models.TextField(null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['cart_signature', 'destination_country']),
            models.Index(fields=['status', 'created_at']),
        ]

class CarrierRate(models.Model):
    """Individual carrier rate response"""
    calculation = models.ForeignKey(ShippingCalculation, on_delete=models.CASCADE)
    carrier_code = models.CharField(max_length=50)
    service_code = models.CharField(max_length=50)
    cost = models.DecimalField(max_digits=6, decimal_places=2)
    estimated_days = models.PositiveIntegerField()
    response_time_ms = models.PositiveIntegerField()
    
    class Meta:
        unique_together = ['calculation', 'carrier_code', 'service_code']
```

**1.3 Shipping Service API**
```python
# services/shipping-service/shipping/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import ShippingCalculation
from .tasks import calculate_shipping_rates_async
from .serializers import ShippingCalculationSerializer

class ShippingCalculationViewSet(viewsets.ModelViewSet):
    queryset = ShippingCalculation.objects.all()
    serializer_class = ShippingCalculationSerializer
    
    def create(self, request):
        """Create new shipping calculation request"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        calculation = serializer.save()
        
        # Start async calculation
        calculate_shipping_rates_async.delay(calculation.calculation_id)
        
        return Response({
            'calculation_id': calculation.calculation_id,
            'status': 'started',
            'poll_url': f'/api/shipping/calculations/{calculation.calculation_id}/'
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['get'])
    def rates(self, request, pk=None):
        """Get all carrier rates for a calculation"""
        calculation = self.get_object()
        rates = calculation.carrierrate_set.all()
        
        return Response({
            'calculation_id': calculation.calculation_id,
            'status': calculation.status,
            'rates': [
                {
                    'carrier': rate.carrier_code,
                    'service': rate.service_code,
                    'cost': rate.cost,
                    'estimated_days': rate.estimated_days
                }
                for rate in rates
            ]
        })
```

**1.4 Shipping Service Tasks**
```python
# services/shipping-service/shipping/tasks.py
from celery import shared_task
from .models import ShippingCalculation, CarrierRate
from .services.carrier_service import CarrierService
import time

@shared_task(bind=True, max_retries=3)
def calculate_shipping_rates_async(self, calculation_id):
    """Calculate shipping rates from all carriers"""
    try:
        calculation = ShippingCalculation.objects.get(
            calculation_id=calculation_id
        )
        
        carrier_service = CarrierService()
        rates = carrier_service.get_all_rates(
            weight=calculation.total_weight,
            volume=calculation.total_volume,
            destination_country=calculation.destination_country,
            destination_postal_code=calculation.destination_postal_code
        )
        
        # Save carrier rates
        for rate_data in rates:
            CarrierRate.objects.create(
                calculation=calculation,
                carrier_code=rate_data['carrier_code'],
                service_code=rate_data['service_code'],
                cost=rate_data['cost'],
                estimated_days=rate_data['estimated_days'],
                response_time_ms=rate_data['response_time_ms']
            )
        
        # Update calculation status
        if rates:
            best_rate = min(rates, key=lambda r: r['cost'])
            calculation.shipping_cost = best_rate['cost']
            calculation.estimated_days = best_rate['estimated_days']
            calculation.carrier_name = best_rate['carrier_code']
            calculation.service_name = best_rate['service_code']
            calculation.status = 'completed'
        else:
            calculation.status = 'failed'
            calculation.error_message = 'No rates available'
        
        calculation.completed_at = timezone.now()
        calculation.save()
        
        # Publish completion event
        publish_shipping_calculated_event(calculation)
        
        return {
            'calculation_id': str(calculation_id),
            'status': calculation.status,
            'rate_count': len(rates)
        }
        
    except Exception as exc:
        calculation.status = 'failed'
        calculation.error_message = str(exc)
        calculation.save()
        
        self.retry(exc=exc, countdown=60)
```

#### Step 2: Extract Packing Service

**2.1 Packing Service Structure**
```python
# services/packing-service/packing/models.py
class PackingCalculation(models.Model):
    """Packing calculation request and result"""
    calculation_id = models.UUIDField(primary_key=True, default=uuid4)
    items_signature = models.CharField(max_length=64, db_index=True)
    
    # Request data
    total_items = models.PositiveIntegerField()
    total_weight = models.DecimalField(max_digits=8, decimal_places=2)
    total_volume = models.DecimalField(max_digits=12, decimal_places=4)
    
    # Results
    packing_cost = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    box_count = models.PositiveIntegerField(null=True)
    utilization_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True)
    
    # Metadata
    status = models.CharField(max_length=20, default='pending')
    algorithm_used = models.CharField(max_length=50, null=True)
    calculation_time_ms = models.PositiveIntegerField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True)

class PackingResult(models.Model):
    """Individual packing result"""
    calculation = models.ForeignKey(PackingCalculation, on_delete=models.CASCADE)
    box_id = models.UUIDField()
    box_title = models.CharField(max_length=100)
    box_cost = models.DecimalField(max_digits=6, decimal_places=2)
    utilization = models.DecimalField(max_digits=5, decimal_places=2)
    items_packed = models.JSONField()
```

**2.2 Packing Service API**
```python
# services/packing-service/packing/views.py
class PackingCalculationViewSet(viewsets.ModelViewSet):
    queryset = PackingCalculation.objects.all()
    
    def create(self, request):
        """Create new packing calculation request"""
        items_data = request.data.get('items', [])
        
        # Create calculation record
        calculation = PackingCalculation.objects.create(
            items_signature=self.generate_items_signature(items_data),
            total_items=len(items_data),
            total_weight=sum(item['weight'] * item['quantity'] for item in items_data),
            total_volume=sum(
                item['length'] * item['width'] * item['height'] * item['quantity']
                for item in items_data
            )
        )
        
        # Start async calculation
        calculate_packing_async.delay(calculation.calculation_id, items_data)
        
        return Response({
            'calculation_id': calculation.calculation_id,
            'status': 'started'
        }, status=status.HTTP_202_ACCEPTED)
```

### Phase 2: Service Communication (Week 3-4)

#### Step 3: Implement Event-Driven Communication

**3.1 Event Bus Setup**
```python
# shared/events/event_bus.py
import json
import redis
from typing import Dict, Any

class EventBus:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    def publish(self, event_type: str, data: Dict[Any, Any]):
        """Publish event to the bus"""
        event = {
            'type': event_type,
            'data': data,
            'timestamp': time.time(),
            'id': str(uuid4())
        }
        
        self.redis_client.publish(f'events:{event_type}', json.dumps(event))
    
    def subscribe(self, event_type: str, callback):
        """Subscribe to events of a specific type"""
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe(f'events:{event_type}')
        
        for message in pubsub.listen():
            if message['type'] == 'message':
                event = json.loads(message['data'])
                callback(event)
```

**3.2 Cart Service Event Handlers**
```python
# services/cart-service/cart/event_handlers.py
from shared.events.event_bus import EventBus

class CartEventHandler:
    def __init__(self):
        self.event_bus = EventBus()
        self.setup_subscriptions()
    
    def setup_subscriptions(self):
        """Setup event subscriptions"""
        self.event_bus.subscribe('shipping_calculated', self.handle_shipping_calculated)
        self.event_bus.subscribe('packing_calculated', self.handle_packing_calculated)
    
    def handle_shipping_calculated(self, event):
        """Handle shipping calculation completion"""
        data = event['data']
        cart_id = data.get('cart_id')
        
        if cart_id:
            # Update cart with shipping information
            cart = Cart.objects.get(id=cart_id)
            cart.shipping_cost = data['shipping_cost']
            cart.estimated_delivery_days = data['estimated_days']
            cart.save()
            
            # Notify frontend via WebSocket
            self.notify_cart_update(cart_id, 'shipping_updated', data)
    
    def handle_packing_calculated(self, event):
        """Handle packing calculation completion"""
        data = event['data']
        cart_id = data.get('cart_id')
        
        if cart_id:
            # Update cart with packing information
            cart = Cart.objects.get(id=cart_id)
            cart.packing_cost = data['packing_cost']
            cart.total_volume = data['total_volume']
            cart.save()
            
            # Notify frontend
            self.notify_cart_update(cart_id, 'packing_updated', data)
```

**3.3 Service Integration**
```python
# services/cart-service/cart/services/integration.py
import requests
from django.conf import settings

class ShippingServiceClient:
    def __init__(self):
        self.base_url = settings.SHIPPING_SERVICE_URL
    
    def calculate_shipping(self, cart_items, destination):
        """Request shipping calculation from shipping service"""
        payload = {
            'cart_signature': self.generate_cart_signature(cart_items),
            'destination_country': destination.country,
            'destination_postal_code': destination.postal_code,
            'total_weight': sum(item.quantity * item.product_variant.weight for item in cart_items),
            'total_volume': sum(
                item.quantity * item.product_variant.length * 
                item.product_variant.width * item.product_variant.height
                for item in cart_items
            ),
            'item_count': len(cart_items)
        }
        
        response = requests.post(
            f'{self.base_url}/api/shipping/calculations/',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 202:
            return response.json()
        else:
            raise Exception(f"Shipping service error: {response.status_code}")

class PackingServiceClient:
    def __init__(self):
        self.base_url = settings.PACKING_SERVICE_URL
    
    def calculate_packing(self, cart_items):
        """Request packing calculation from packing service"""
        items_data = []
        for item in cart_items:
            for _ in range(item.quantity):
                items_data.append({
                    'sku': item.product_variant.sku,
                    'weight': float(item.product_variant.weight),
                    'length': float(item.product_variant.length),
                    'width': float(item.product_variant.width),
                    'height': float(item.product_variant.height)
                })
        
        payload = {'items': items_data}
        
        response = requests.post(
            f'{self.base_url}/api/packing/calculations/',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 202:
            return response.json()
        else:
            raise Exception(f"Packing service error: {response.status_code}")
```

### Phase 3: API Gateway and Load Balancing (Week 5-6)

#### Step 4: Implement API Gateway

**4.1 API Gateway Configuration**
```yaml
# api-gateway/kong.yml
_format_version: "2.1"

services:
  - name: cart-service
    url: http://cart-service:8000
    routes:
      - name: cart-routes
        paths:
          - /api/cart
        strip_path: false

  - name: shipping-service
    url: http://shipping-service:8001
    routes:
      - name: shipping-routes
        paths:
          - /api/shipping
        strip_path: false

  - name: packing-service
    url: http://packing-service:8002
    routes:
      - name: packing-routes
        paths:
          - /api/packing
        strip_path: false

plugins:
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000
  
  - name: cors
    config:
      origins:
        - http://localhost:3000
        - https://picky-store.netlify.app
      
  - name: prometheus
    config:
      per_consumer: true
```

**4.2 Load Balancer Configuration**
```nginx
# nginx/nginx.conf
upstream cart-service {
    least_conn;
    server cart-service-1:8000 weight=1 max_fails=3 fail_timeout=30s;
    server cart-service-2:8000 weight=1 max_fails=3 fail_timeout=30s;
    server cart-service-3:8000 weight=1 max_fails=3 fail_timeout=30s;
}

upstream shipping-service {
    least_conn;
    server shipping-service-1:8001 weight=1 max_fails=3 fail_timeout=30s;
    server shipping-service-2:8001 weight=1 max_fails=3 fail_timeout=30s;
}

upstream packing-service {
    least_conn;
    server packing-service-1:8002 weight=1 max_fails=3 fail_timeout=30s;
    server packing-service-2:8002 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    
    location /api/cart/ {
        proxy_pass http://cart-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    location /api/shipping/ {
        proxy_pass http://shipping-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /api/packing/ {
        proxy_pass http://packing-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

## Deployment Strategy

### Docker Containerization

**Cart Service Dockerfile:**
```dockerfile
# services/cart-service/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "cart_service.wsgi:application"]
```

**Docker Compose for Development:**
```yaml
# docker-compose.yml
version: '3.8'

services:
  cart-service:
    build: ./services/cart-service
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/cart_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis

  shipping-service:
    build: ./services/shipping-service
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=************************************/shipping_db
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - postgres
      - redis

  packing-service:
    build: ./services/packing-service
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=************************************/packing_db
      - REDIS_URL=redis://redis:6379/2
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=picky_store
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - cart-service
      - shipping-service
      - packing-service

volumes:
  postgres_data:
  redis_data:
```

## Expected Benefits

### Performance Improvements

| Metric | Monolithic | Microservices | Improvement |
|--------|------------|---------------|-------------|
| Cart Operations | 800ms | 50ms | 16x faster |
| Shipping Calculations | 1,500ms | 200ms | 7.5x faster |
| Packing Calculations | 300ms | 100ms | 3x faster |
| Concurrent Users | 100 | 10,000+ | 100x increase |

### Scalability Benefits

1. **Independent Scaling**: Scale each service based on demand
2. **Fault Isolation**: Failure in one service doesn't affect others
3. **Technology Diversity**: Use best technology for each service
4. **Team Independence**: Teams can work independently on services

### Operational Benefits

1. **Easier Deployment**: Deploy services independently
2. **Better Monitoring**: Service-specific metrics and alerts
3. **Improved Debugging**: Isolated service logs and traces
4. **Faster Development**: Parallel development across teams

## Next Steps

1. **Start with Shipping Service extraction** (highest impact)
2. **Implement event-driven communication** for loose coupling
3. **Add comprehensive monitoring** for each service
4. **Gradually migrate remaining functionality** to microservices
5. **Implement advanced patterns** (circuit breakers, bulkheads, etc.)

This microservices architecture provides the foundation for handling massive scale while maintaining system reliability and developer productivity.
