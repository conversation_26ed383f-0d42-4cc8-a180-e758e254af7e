# Caching Strategy Implementation Guide

## Overview

Implementing a comprehensive caching strategy can provide 10-50x performance improvements for cart operations. This guide covers Redis-based caching for shipping calculations, packing results, and frequently accessed data.

## Current Caching Issues

### Minimal Caching Implementation
- Limited caching in shipping service (1-hour TTL)
- No cache invalidation strategy
- No caching for packing calculations
- Missing cache warming mechanisms

### Performance Impact
- Repeated expensive calculations
- Redundant external API calls
- High database load for read operations
- Poor cache hit rates (< 20%)

## Redis Caching Architecture

### Cache Layers

```mermaid
graph TD
    A[User Request] --> B[Application Cache]
    B --> C[Database Cache]
    B --> D[Shipping Cache]
    B --> E[Packing Cache]
    D --> F[External API Cache]
    
    B --> G[Redis Cluster]
    C --> G
    D --> G
    E --> G
    F --> G
```

### Cache Key Strategy

**Hierarchical Key Structure:**
```
picky:cart:{cart_id}:basic
picky:shipping:{cart_signature}:{destination_hash}
picky:packing:{items_signature}
picky:carrier:{carrier_code}:{package_hash}
picky:product:{product_id}:details
```

## Step-by-Step Implementation

### Phase 1: Basic Redis Setup (Day 1-2)

#### Step 1: Install and Configure Redis

**1.1 Install Redis**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# Configure Redis for production
sudo nano /etc/redis/redis.conf
```

**1.2 Redis Configuration**
```conf
# /etc/redis/redis.conf - Production settings
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 300

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence
save 900 1
save 300 10
save 60 10000

# Performance
tcp-backlog 511
databases 16
```

**1.3 Django Redis Configuration**
```python
# pc_hardware/settings/common.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 100,
                'retry_on_timeout': True,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'picky',
        'TIMEOUT': 3600,  # 1 hour default
    },
    'shipping': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'picky:shipping',
        'TIMEOUT': 1800,  # 30 minutes
    },
    'packing': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/3',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'picky:packing',
        'TIMEOUT': 7200,  # 2 hours
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

#### Step 2: Implement Cart Caching

**2.1 Create Cart Cache Service**
```python
# apps/cart/services/cache.py
import hashlib
import json
from django.core.cache import caches
from django.core.cache.utils import make_template_fragment_key
from django.utils import timezone
from decimal import Decimal

class CartCacheService:
    def __init__(self):
        self.cache = caches['default']
        self.shipping_cache = caches['shipping']
        self.packing_cache = caches['packing']
    
    def get_cart_cache_key(self, cart_id, suffix='basic'):
        """Generate cart cache key"""
        return f"cart:{cart_id}:{suffix}"
    
    def cache_cart_data(self, cart, timeout=3600):
        """Cache basic cart data"""
        cache_key = self.get_cart_cache_key(cart.id)
        
        cart_data = {
            'id': str(cart.id),
            'customer_id': cart.customer_id,
            'item_count': cart.cart_items.count(),
            'total_weight': float(cart.get_cart_weight()),
            'total_price': float(cart.total_price) if hasattr(cart, 'total_price') else 0,
            'last_updated': timezone.now().isoformat(),
        }
        
        self.cache.set(cache_key, cart_data, timeout)
        return cart_data
    
    def get_cached_cart_data(self, cart_id):
        """Get cached cart data"""
        cache_key = self.get_cart_cache_key(cart_id)
        return self.cache.get(cache_key)
    
    def invalidate_cart_cache(self, cart_id):
        """Invalidate all cart-related cache entries"""
        patterns = [
            f"cart:{cart_id}:*",
            f"shipping:*:{cart_id}:*",
            f"packing:*:{cart_id}:*"
        ]
        
        for pattern in patterns:
            self.cache.delete_pattern(pattern)
    
    def generate_cart_signature(self, cart_items):
        """Generate deterministic signature for cart items"""
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'dimensions': [
                    float(item.product_variant.length),
                    float(item.product_variant.width),
                    float(item.product_variant.height)
                ]
            })
        
        # Sort for consistency
        items_data.sort(key=lambda x: x['sku'])
        
        # Generate hash
        signature = hashlib.md5(
            json.dumps(items_data, sort_keys=True).encode('utf-8')
        ).hexdigest()
        
        return signature
```

**2.2 Integrate Cache with Cart Operations**
```python
# apps/cart/serializers.py - Updated with caching
from .services.cache import CartCacheService

class AddCartItemSerializer(ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_service = CartCacheService()
    
    def save(self, **kwargs):
        cart_id = self.context['cart_id']
        
        # Invalidate cache before modification
        self.cache_service.invalidate_cart_cache(cart_id)
        
        # Existing save logic...
        result = super().save(**kwargs)
        
        # Cache updated cart data
        cart = Cart.objects.get(id=cart_id)
        self.cache_service.cache_cart_data(cart)
        
        return result
```

### Phase 2: Shipping Calculation Caching (Day 3-4)

#### Step 3: Implement Shipping Cache

**3.1 Enhanced Shipping Cache Service**
```python
# apps/shipping/services/cache.py
import hashlib
import json
from django.core.cache import caches
from django.utils import timezone
from datetime import timedelta

class ShippingCacheService:
    def __init__(self):
        self.cache = caches['shipping']
        self.default_timeout = 1800  # 30 minutes
        self.carrier_timeout = 900   # 15 minutes for carrier rates
    
    def generate_shipping_cache_key(self, cart_items, destination, carrier_code=None):
        """Generate cache key for shipping calculations"""
        # Cart signature
        cart_signature = self._generate_cart_signature(cart_items)
        
        # Destination signature
        dest_data = {
            'country': destination.country if destination else 'US',
            'state': getattr(destination, 'state', ''),
            'postal_code': getattr(destination, 'postal_code', ''),
        }
        dest_signature = hashlib.md5(
            json.dumps(dest_data, sort_keys=True).encode('utf-8')
        ).hexdigest()[:8]
        
        # Carrier-specific or general
        if carrier_code:
            return f"carrier:{carrier_code}:{cart_signature}:{dest_signature}"
        else:
            return f"shipping:{cart_signature}:{dest_signature}"
    
    def get_cached_shipping_calculation(self, cart_items, destination):
        """Get cached shipping calculation"""
        cache_key = self.generate_shipping_cache_key(cart_items, destination)
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            # Update access time for LRU
            cached_data['last_accessed'] = timezone.now().isoformat()
            self.cache.set(cache_key, cached_data, self.default_timeout)
            
            return {
                'success': True,
                'cached': True,
                'cache_key': cache_key,
                **cached_data['result']
            }
        
        return None
    
    def cache_shipping_calculation(self, cart_items, destination, result):
        """Cache shipping calculation result"""
        cache_key = self.generate_shipping_cache_key(cart_items, destination)
        
        cache_data = {
            'result': result,
            'cached_at': timezone.now().isoformat(),
            'last_accessed': timezone.now().isoformat(),
            'cart_signature': self._generate_cart_signature(cart_items),
        }
        
        self.cache.set(cache_key, cache_data, self.default_timeout)
        return cache_key
    
    def get_cached_carrier_rate(self, carrier_code, packing_result, destination):
        """Get cached carrier-specific rate"""
        cache_key = f"carrier:{carrier_code}:{self._generate_packing_signature(packing_result)}:{self._generate_destination_signature(destination)}"
        return self.cache.get(cache_key)
    
    def cache_carrier_rate(self, carrier_code, packing_result, destination, rate):
        """Cache carrier-specific rate"""
        cache_key = f"carrier:{carrier_code}:{self._generate_packing_signature(packing_result)}:{self._generate_destination_signature(destination)}"
        
        cache_data = {
            'rate': rate,
            'cached_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(seconds=self.carrier_timeout)).isoformat(),
        }
        
        self.cache.set(cache_key, cache_data, self.carrier_timeout)
        return cache_key
    
    def _generate_cart_signature(self, cart_items):
        """Generate cart signature for caching"""
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'dimensions': [
                    float(item.product_variant.length),
                    float(item.product_variant.width),
                    float(item.product_variant.height)
                ]
            })
        
        items_data.sort(key=lambda x: x['sku'])
        return hashlib.md5(
            json.dumps(items_data, sort_keys=True).encode('utf-8')
        ).hexdigest()[:16]
    
    def _generate_packing_signature(self, packing_result):
        """Generate packing result signature"""
        packing_data = {
            'total_weight': float(packing_result.total_weight),
            'total_volume': float(packing_result.total_volume),
            'box_count': len(packing_result.boxes),
        }
        return hashlib.md5(
            json.dumps(packing_data, sort_keys=True).encode('utf-8')
        ).hexdigest()[:12]
    
    def _generate_destination_signature(self, destination):
        """Generate destination signature"""
        dest_data = {
            'country': destination.country if destination else 'US',
            'postal_code': getattr(destination, 'postal_code', ''),
        }
        return hashlib.md5(
            json.dumps(dest_data, sort_keys=True).encode('utf-8')
        ).hexdigest()[:8]
```

**3.2 Update Shipping Service with Caching**
```python
# apps/shipping/services/shipping.py - Updated with caching
from .cache import ShippingCacheService

class ShippingService:
    def __init__(self):
        self.cache_service = ShippingCacheService()
        # ... existing initialization
    
    def calculate_shipping_cost(self, packing_result, destination_address=None):
        """Calculate shipping cost with caching"""
        
        # Try to get from cache first
        if destination_address:
            cached_result = self.cache_service.get_cached_shipping_calculation(
                packing_result, destination_address
            )
            if cached_result:
                self.logger.debug(f"Using cached shipping rate: {cached_result}")
                return cached_result
        
        # Generate cache key for fallback
        cache_key = self._generate_cache_key(packing_result, destination_address)
        cached_rate = cache.get(cache_key)
        
        if cached_rate:
            self.logger.debug(f"Using Redis cached shipping rate: {cached_rate.cost}")
            return cached_rate
        
        # Calculate new rate
        best_rate = self._calculate_fresh_rate(packing_result, destination_address)
        
        # Cache the result
        if best_rate and destination_address:
            cache.set(cache_key, best_rate, self.cache_timeout)
            self.cache_service.cache_shipping_calculation(
                packing_result, destination_address, best_rate
            )
        
        return best_rate
    
    def get_shipping_rate_from_carrier(self, carrier, packing_result, destination):
        """Get rate from carrier with caching"""
        
        # Check carrier-specific cache
        cached_rate = self.cache_service.get_cached_carrier_rate(
            carrier.code, packing_result, destination
        )
        
        if cached_rate and not self._is_cache_expired(cached_rate):
            return cached_rate['rate']
        
        # Get fresh rate from carrier
        try:
            rate = carrier.get_shipping_rate(packing_result, destination)
            
            # Cache the rate
            if rate:
                self.cache_service.cache_carrier_rate(
                    carrier.code, packing_result, destination, rate
                )
            
            return rate
            
        except Exception as e:
            self.logger.warning(f"Carrier {carrier.code} failed: {e}")
            
            # Return cached rate even if expired as fallback
            if cached_rate:
                return cached_rate['rate']
            
            return None
    
    def _is_cache_expired(self, cached_data):
        """Check if cached data is expired"""
        if 'expires_at' not in cached_data:
            return False
        
        expires_at = timezone.datetime.fromisoformat(cached_data['expires_at'])
        return timezone.now() > expires_at
```

### Phase 3: Packing Calculation Caching (Day 5-6)

#### Step 4: Implement Packing Cache

**4.1 Packing Cache Service**
```python
# apps/shipping/services/packing_cache.py
import hashlib
import json
from django.core.cache import caches
from django.utils import timezone

class PackingCacheService:
    def __init__(self):
        self.cache = caches['packing']
        self.timeout = 7200  # 2 hours
    
    def generate_packing_cache_key(self, cart_items):
        """Generate cache key for packing calculations"""
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'length': float(item.product_variant.length),
                'width': float(item.product_variant.width),
                'height': float(item.product_variant.height),
                'product_type': item.product.product_type.name,
            })
        
        # Sort for consistency
        items_data.sort(key=lambda x: (x['sku'], x['quantity']))
        
        # Generate hash
        signature = hashlib.md5(
            json.dumps(items_data, sort_keys=True).encode('utf-8')
        ).hexdigest()
        
        return f"packing:{signature}"
    
    def get_cached_packing_result(self, cart_items):
        """Get cached packing result"""
        cache_key = self.generate_packing_cache_key(cart_items)
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            # Reconstruct PackingResult object
            from ..packing import PackingResult, PackedBox
            
            # Update access time
            cached_data['last_accessed'] = timezone.now().isoformat()
            self.cache.set(cache_key, cached_data, self.timeout)
            
            return self._deserialize_packing_result(cached_data['result'])
        
        return None
    
    def cache_packing_result(self, cart_items, packing_result):
        """Cache packing calculation result"""
        cache_key = self.generate_packing_cache_key(cart_items)
        
        cache_data = {
            'result': self._serialize_packing_result(packing_result),
            'cached_at': timezone.now().isoformat(),
            'last_accessed': timezone.now().isoformat(),
            'item_count': len(cart_items),
        }
        
        self.cache.set(cache_key, cache_data, self.timeout)
        return cache_key
    
    def _serialize_packing_result(self, packing_result):
        """Serialize PackingResult for caching"""
        return {
            'total_cost': float(packing_result.total_cost),
            'total_weight': float(packing_result.total_weight),
            'total_volume': float(packing_result.total_volume),
            'success': packing_result.success,
            'method_used': packing_result.method_used,
            'calculation_time': packing_result.calculation_time,
            'warnings': packing_result.warnings,
            'boxes': [self._serialize_packed_box(box) for box in packing_result.boxes],
            'unpacked_items': packing_result.unpacked_items,
        }
    
    def _serialize_packed_box(self, packed_box):
        """Serialize PackedBox for caching"""
        return {
            'box_id': packed_box.box.id,
            'box_title': packed_box.box.title,
            'utilization': float(packed_box.utilization),
            'total_weight': float(packed_box.total_weight),
            'total_cost': float(packed_box.total_cost),
            'efficiency_score': float(packed_box.efficiency_score),
            'items': [self._serialize_packed_item(item) for item in packed_box.items],
        }
    
    def _serialize_packed_item(self, packed_item):
        """Serialize PackedItem for caching"""
        return {
            'sku': packed_item.sku,
            'quantity': packed_item.quantity,
            'weight': float(packed_item.weight),
            'volume': float(packed_item.volume),
        }
    
    def _deserialize_packing_result(self, data):
        """Deserialize cached data back to PackingResult"""
        from ..packing import PackingResult, PackedBox, PackedItem
        from ..models import Box
        from decimal import Decimal
        
        # Reconstruct boxes
        boxes = []
        for box_data in data['boxes']:
            try:
                box = Box.objects.get(id=box_data['box_id'])
                
                # Reconstruct items
                items = []
                for item_data in box_data['items']:
                    items.append(PackedItem(
                        sku=item_data['sku'],
                        quantity=item_data['quantity'],
                        weight=Decimal(str(item_data['weight'])),
                        volume=Decimal(str(item_data['volume']))
                    ))
                
                boxes.append(PackedBox(
                    box=box,
                    items=items,
                    utilization=box_data['utilization'],
                    total_weight=Decimal(str(box_data['total_weight'])),
                    total_cost=Decimal(str(box_data['total_cost'])),
                    efficiency_score=box_data['efficiency_score']
                ))
            except Box.DoesNotExist:
                # Box was deleted, invalidate cache
                continue
        
        return PackingResult(
            boxes=boxes,
            total_cost=Decimal(str(data['total_cost'])),
            total_weight=Decimal(str(data['total_weight'])),
            total_volume=Decimal(str(data['total_volume'])),
            unpacked_items=data['unpacked_items'],
            success=data['success'],
            calculation_time=data['calculation_time'],
            method_used=data['method_used'],
            warnings=data['warnings']
        )
```

**4.2 Update Packing Service with Caching**
```python
# apps/shipping/services/packing.py - Updated with caching
from .packing_cache import PackingCacheService

class PackingService:
    def __init__(self):
        self.cache_service = PackingCacheService()
        # ... existing initialization
    
    def calculate_optimal_packaging(self, cart_items):
        """Calculate optimal packaging with caching"""
        import time
        start_time = time.time()
        
        # Try to get from cache first
        cached_result = self.cache_service.get_cached_packing_result(cart_items)
        if cached_result:
            self.logger.debug(f"Using cached packing result for {len(cart_items)} items")
            cached_result.calculation_time = time.time() - start_time
            return cached_result
        
        # Calculate fresh result
        result = self._calculate_fresh_packing(cart_items)
        
        # Cache the result
        if result.success:
            self.cache_service.cache_packing_result(cart_items, result)
        
        result.calculation_time = time.time() - start_time
        return result
    
    def _calculate_fresh_packing(self, cart_items):
        """Calculate packing without cache"""
        # Existing packing logic...
        return super().calculate_optimal_packaging(cart_items)
```

## Cache Invalidation Strategy

### Automatic Invalidation

**Cart-level Invalidation:**
```python
# apps/cart/signals.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Cart, CartItem
from .services.cache import CartCacheService

@receiver([post_save, post_delete], sender=CartItem)
def invalidate_cart_cache(sender, instance, **kwargs):
    """Invalidate cart cache when items change"""
    cache_service = CartCacheService()
    cache_service.invalidate_cart_cache(instance.cart_id)

@receiver(post_save, sender=Cart)
def invalidate_cart_on_update(sender, instance, **kwargs):
    """Invalidate cart cache when cart is updated"""
    if kwargs.get('update_fields'):
        cache_service = CartCacheService()
        cache_service.invalidate_cart_cache(instance.id)
```

**Product-level Invalidation:**
```python
# apps/products/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import ProductVariant
from django.core.cache import caches

@receiver(post_save, sender=ProductVariant)
def invalidate_product_cache(sender, instance, **kwargs):
    """Invalidate caches when product variant changes"""
    if kwargs.get('update_fields'):
        # Invalidate packing cache for this SKU
        packing_cache = caches['packing']
        packing_cache.delete_pattern(f"*{instance.sku}*")
        
        # Invalidate shipping cache for carts containing this item
        shipping_cache = caches['shipping']
        shipping_cache.delete_pattern(f"*{instance.sku}*")
```

### Manual Cache Management

**Cache Management Commands:**
```python
# apps/core/management/commands/manage_cache.py
from django.core.management.base import BaseCommand
from django.core.cache import caches

class Command(BaseCommand):
    help = 'Manage application caches'
    
    def add_arguments(self, parser):
        parser.add_argument('action', choices=['clear', 'stats', 'warm'])
        parser.add_argument('--cache', default='all')
    
    def handle(self, *args, **options):
        action = options['action']
        cache_name = options['cache']
        
        if action == 'clear':
            self.clear_cache(cache_name)
        elif action == 'stats':
            self.show_stats(cache_name)
        elif action == 'warm':
            self.warm_cache()
    
    def clear_cache(self, cache_name):
        if cache_name == 'all':
            for name in ['default', 'shipping', 'packing']:
                caches[name].clear()
                self.stdout.write(f"Cleared {name} cache")
        else:
            caches[cache_name].clear()
            self.stdout.write(f"Cleared {cache_name} cache")
    
    def show_stats(self, cache_name):
        # Implementation for cache statistics
        pass
    
    def warm_cache(self):
        # Implementation for cache warming
        pass
```

## Performance Monitoring

### Cache Hit Rate Monitoring

```python
# apps/core/monitoring/cache_monitor.py
from django.core.cache import caches
import redis

class CacheMonitor:
    def __init__(self):
        self.redis_client = redis.Redis(host='127.0.0.1', port=6379, db=0)
    
    def get_cache_stats(self):
        """Get comprehensive cache statistics"""
        info = self.redis_client.info()
        
        stats = {
            'memory_usage': info['used_memory_human'],
            'connected_clients': info['connected_clients'],
            'total_commands_processed': info['total_commands_processed'],
            'keyspace_hits': info['keyspace_hits'],
            'keyspace_misses': info['keyspace_misses'],
            'hit_rate': info['keyspace_hits'] / (info['keyspace_hits'] + info['keyspace_misses']) * 100
        }
        
        return stats
    
    def get_cache_keys_by_pattern(self, pattern):
        """Get cache keys matching pattern"""
        return self.redis_client.keys(pattern)
    
    def monitor_cache_performance(self):
        """Monitor cache performance metrics"""
        stats = self.get_cache_stats()
        
        # Log metrics
        print(f"Cache Hit Rate: {stats['hit_rate']:.2f}%")
        print(f"Memory Usage: {stats['memory_usage']}")
        print(f"Connected Clients: {stats['connected_clients']}")
        
        return stats
```

## Expected Performance Improvements

### Cache Hit Rate Targets

| Cache Type | Target Hit Rate | Expected Improvement |
|------------|----------------|---------------------|
| Shipping Calculations | 80-90% | 10-20x faster |
| Packing Results | 70-85% | 5-15x faster |
| Product Data | 95%+ | 50x faster |
| Cart Data | 90%+ | 10x faster |

### Response Time Improvements

| Operation | Before Caching | After Caching | Improvement |
|-----------|----------------|---------------|-------------|
| Add to Cart | 800ms | 80ms | 10x faster |
| Update Quantity | 600ms | 60ms | 10x faster |
| Shipping Calculation | 1,500ms | 150ms | 10x faster |
| Cart Retrieval | 300ms | 30ms | 10x faster |

## Next Steps

1. **Implement Redis setup** (Day 1)
2. **Deploy cart caching** (Day 2)
3. **Add shipping cache** (Day 3-4)
4. **Implement packing cache** (Day 5-6)
5. **Monitor and optimize** (Ongoing)
6. **Proceed to Async Processing** (04-Async-Processing.md) for further improvements
