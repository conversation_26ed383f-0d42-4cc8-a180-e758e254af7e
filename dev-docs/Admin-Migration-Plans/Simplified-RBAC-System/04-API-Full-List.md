# API Full List - Staff RBAC System (v3.0)

## **Complete API Reference**

### **System Overview**

This document provides a comprehensive list of all available API endpoints in the Staff RBAC system. The system is built on Django's permission framework with custom enhancements for staff management.

**Base URL:** `/api/staff/`

**Authentication:** JWT tokens via core app authentication endpoints

**Current Implementation Status:**
- ✅ **Authorization/RBAC**: Fully implemented
- 🚧 **Product Management**: Planned (not implemented)
- 🚧 **Order Management**: Planned (not implemented)
- 🚧 **Customer Management**: Planned (not implemented)

---

## **Authentication & User Information Endpoints**

### **1. Get Current Staff User Info**

**Available requests:** [GET]

**GET:** `/api/staff/auth/user/` [Staff Users]

Retrieves current authenticated staff user information including groups, permissions, and staff profile.

```json
No request body required
```

**Response Example:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "is_staff": true,
    "is_superuser": false,
    "groups": ["Staff Manager (SM)"],
    "permissions": ["auth.add_user", "auth.change_user", "core.can_toggle_staff_status"],
    "staff_profile": {
      "employee_id": "EMP001",
      "department": "ADMIN",
      "position_title": "System Administrator",
      "status": "ACTIVE"
    }
  }
}
```

---

### **2. Get Detailed User Permissions**

**Available requests:** [GET]

**GET:** `/api/staff/auth/permissions/` [Staff Users]

Gets detailed permission breakdown for the current user including group-specific permissions.

```json
No request body required
```

**Response Example:**
```json
{
  "success": true,
  "user_id": 1,
  "email": "<EMAIL>",
  "is_superuser": false,
  "groups": ["Staff Manager (SM)"],
  "permissions": ["auth.add_user", "auth.change_user", "core.can_toggle_staff_status"],
  "group_permissions": {
    "Staff Manager (SM)": ["auth.add_user", "auth.change_user", "core.can_toggle_staff_status"]
  }
}
```

---

### **3. Check Specific Permission**

**Available requests:** [POST]

**POST:** `/api/staff/auth/check-permission/` [Staff Users]

Validates if the current user has a specific permission.

```json
{
  "permission": "auth.add_user"
}
```

**Response Example:**
```json
{
  "success": true,
  "permission": "auth.add_user",
  "has_permission": true
}
```

---

## **Role Management Endpoints**

### **4. List All Roles**

**Available requests:** [GET, POST]

**GET:** `/api/staff/roles/` [Staff Users with Group View Permission]

Retrieves all roles (Django Groups) with search and filtering capabilities.

**Query Parameters:**
- `search` - Filter by role name
- `permission_id` - Filter by permission ID

```json
No request body required
```

**POST:** `/api/staff/roles/` [Staff Users with Group Management Permission]

Creates a new role with specified permissions.

```json
{
  "name": "New Role Name (NRN)",
  "permission_ids": [1, 2, 3]
}
```

---

### **5. Get Role Details**

**Available requests:** [GET, PUT, PATCH, DELETE]

**GET:** `/api/staff/roles/{id}/` [Staff Users with Group View Permission]

Retrieves detailed role information including permissions and member counts.

**PUT/PATCH:** `/api/staff/roles/{id}/` [Staff Users with Group Management Permission]

Updates role information and permissions.

**DELETE:** `/api/staff/roles/{id}/` [Staff Users with Group Delete Permission]

Deletes a role and removes all user memberships.

---

### **6. Get Role Members**

**Available requests:** [GET]

**GET:** `/api/staff/roles/{id}/members/` [Staff Users]

Gets all users assigned to a specific role.

**Response Example:**
```json
{
  "success": true,
  "role": "Staff Manager (SM)",
  "total_members": 3,
  "staff_members": 2,
  "members": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "is_staff": true
    }
  ]
}
```

---

### **7. Get Role Permissions**

**Available requests:** [GET]

**GET:** `/api/staff/roles/{id}/permissions/` [Staff Users]

Gets detailed permission breakdown for a role.

**Response Example:**
```json
{
  "success": true,
  "role": "Staff Manager (SM)",
  "permission_count": 8,
  "permission_summary": {
    "auth": [
      {"codename": "add_user", "name": "Can add user", "model": "user"},
      {"codename": "change_user", "name": "Can change user", "model": "user"}
    ],
    "core": [
      {"codename": "can_toggle_staff_status", "name": "Can toggle staff status for users", "model": "user"}
    ]
  },
  "permission_codenames": ["auth.add_user", "auth.change_user", "core.can_toggle_staff_status"]
}
```

---

### **8. Add Permission to Role**

**Available requests:** [POST]

**POST:** `/api/staff/roles/{id}/add_permission/` [Staff Users with Group Management Permission]

Adds a permission to a role.

```json
{
  "permission_codename": "auth.add_user"
}
```

---

### **9. Remove Permission from Role**

**Available requests:** [POST]

**POST:** `/api/staff/roles/{id}/remove_permission/` [Staff Users with Group Management Permission]

Removes a permission from a role.

```json
{
  "permission_codename": "auth.delete_user"
}
```

---

## **Staff Profile Management Endpoints**

### **10. List Staff Profiles**

**Available requests:** [GET, POST]

**GET:** `/api/staff/staff-profiles/` [Staff Users with Staff Management Permission]

Retrieves staff profiles with department filtering and organizational hierarchy. Access is filtered based on user role:
- **Superusers & Staff Managers**: See all profiles
- **Department Heads**: See their department and direct reports
- **HR Administrators**: See all profiles
- **Regular Staff**: See only their own profile

**Query Parameters:**
- `department` - Filter by department (PRODUCT, ORDER, CUSTOMER, etc.)
- `search` - Search by name or employee ID
- `status` - Filter by status (ACTIVE, INACTIVE, ON_LEAVE, TERMINATED)

```json
No request body required
```

**POST:** `/api/staff/staff-profiles/` [Staff Users with Staff Management Permission]

Creates a new staff profile.

```json
{
  "user": 1,
  "employee_id": "EMP001",
  "department": "PRODUCT",
  "position_title": "Product Manager",
  "manager": 2,
  "hire_date": "2024-01-15",
  "notes": "New hire from competitor"
}
```

---

### **11. Get Staff Profile Details**

**Available requests:** [GET, PUT, PATCH, DELETE]

**GET:** `/api/staff/staff-profiles/{id}/` [Staff Users with Staff Management Permission]

Retrieves detailed staff profile information including management chain and team data.

**PUT/PATCH:** `/api/staff/staff-profiles/{id}/` [Staff Users with Staff Management Permission]

Updates staff profile information with circular dependency validation.

**DELETE:** `/api/staff/staff-profiles/{id}/` [Staff Users with Staff Management Permission]

Soft deletes a staff profile (sets status to TERMINATED).

---

### **12. Get Direct Reports**

**Available requests:** [GET]

**GET:** `/api/staff/staff-profiles/{id}/direct_reports/` [Staff Users with Staff Management Permission]

Gets all direct reports for a staff member.

**Response Example:**
```json
{
  "success": true,
  "manager": "John Smith",
  "team_size": 3,
  "direct_reports": [
    {
      "id": 2,
      "employee_id": "EMP002",
      "user": {"email": "<EMAIL>"},
      "position_title": "Product Specialist",
      "department": "PRODUCT",
      "status": "ACTIVE"
    }
  ]
}
```

---

### **13. Change Staff Status**

**Available requests:** [POST]

**POST:** `/api/staff/staff-profiles/{id}/change_status/` [Staff Users with Staff Management Permission]

Changes the status of a staff member with audit logging.

```json
{
  "status": "INACTIVE"
}
```

**Valid Status Options:**
- `ACTIVE` - Working normally
- `INACTIVE` - Temporarily disabled
- `ON_LEAVE` - On leave but still employed
- `TERMINATED` - No longer with company

---

### **14. Department Summary**

**Available requests:** [GET]

**GET:** `/api/staff/staff-profiles/department_summary/` [Staff Users with Staff Management Permission]

Gets summary statistics of staff by department.

**Response Example:**
```json
{
  "success": true,
  "department_summary": {
    "PRODUCT": {
      "name": "Product Management",
      "total_staff": 8,
      "managers": 2
    },
    "ORDER": {
      "name": "Order Management",
      "total_staff": 5,
      "managers": 1
    }
  }
}
```

---

## **Staff User Creation Endpoints**

### **15. Create Complete Staff User**

**Available requests:** [POST]

**POST:** `/api/staff/staff-management/create_staff_user/` [Staff Users with Staff Creation Permission]

Creates a new staff user with profile and role assignments in a single transaction.

```json
{
  "email": "<EMAIL>",
  "employee_id": "EMP002",
  "department": "ORDER",
  "position_title": "Order Specialist",
  "manager_id": 1,
  "hire_date": "2024-01-20",
  "group_ids": [3, 4],
  "notes": "Experienced hire from competitor"
}
```

**Response Example:**
```json
{
  "success": true,
  "message": "Staff user created successfully",
  "user_id": 5,
  "staff_profile_id": 3,
  "roles_assigned": 2,
  "employee_id": "EMP002"
}
```

---

## **Group Management Endpoints**

### **16. List All Groups**

**Available requests:** [GET, POST]

**GET:** `/api/staff/groups/` [Staff Users with Group View Permission]

Retrieves all available groups with search and filtering capabilities.

**Query Parameters:**
- `search` - Filter by group name
- `permission_id` - Filter by permission ID

```json
No request body required
```

**POST:** `/api/staff/groups/` [Staff Users with Group Management Permission]

Creates a new group with specified permissions.

```json
{
  "name": "New Staff Group",
  "permission_ids": [25, 26, 27, 28]
}
```

---

### **17. Group Detail Operations**

**Available requests:** [GET, PUT, PATCH, DELETE]

**GET:** `/api/staff/groups/{group_id}/` [Staff Users with Group View Permission]

Retrieves detailed information about a specific group including members and permissions.

**PUT:** `/api/staff/groups/{group_id}/` [Staff Users with Group Management Permission]

Updates all fields of a specific group including name and permissions.

```json
{
  "name": "Updated Group Name",
  "permission_ids": [25, 26, 27]
}
```

**PATCH:** `/api/staff/groups/{group_id}/` [Staff Users with Group Management Permission]

Partially updates specific fields of a group.

```json
{
  "name": "Partially Updated Group Name"
}
```

**DELETE:** `/api/staff/groups/{group_id}/` [Staff Users with Group Delete Permission]

Permanently deletes a group and removes all user memberships.

---

### **18. Get Group Members**

**Available requests:** [GET]

**GET:** `/api/staff/groups/{group_id}/members/` [Staff Users]

Retrieves all members of a specific group with membership details.

**Response Example:**
```json
{
  "success": true,
  "members": [
    {
      "id": 1,
      "user": {"email": "<EMAIL>"},
      "group": {"name": "Staff Manager (SM)"},
      "assigned_by": {"email": "<EMAIL>"},
      "assigned_at": "2024-01-15T10:30:00Z",
      "is_active": true,
      "notes": "Initial assignment"
    }
  ]
}
```

---

### **19. Add User to Group**

**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/add_member/` [Staff Users with Group Management Permission]

Adds any active user (staff or regular) to a specific group with optional notes.

```json
{
  "user_id": 5,
  "notes": "Adding user to product management team"
}
```

---

### **20. Remove User from Group**

**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/remove_member/` [Staff Users with Group Management Permission]

Removes a user from a specific group.

```json
{
  "user_id": 5
}
```

---

### **21. Bulk User Assignment to Group**

**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/bulk_assign/` [Staff Users with Group Management Permission]

Assigns multiple active users (staff or regular) to a group in a single operation.

```json
{
  "user_ids": [5, 6, 7],
  "notes": "Bulk assignment to product management team"
}
```

**Response Example:**
```json
{
  "success": true,
  "message": "3 users assigned to group Staff Manager (SM)",
  "results": [
    {"user_email": "<EMAIL>", "assigned": true},
    {"user_email": "<EMAIL>", "assigned": true},
    {"user_email": "<EMAIL>", "assigned": false}
  ]
}
```

---

## **User Management Endpoints**

### **22. List Users**

**Available requests:** [GET]

**GET:** `/api/staff/users/` [Staff Users with User Management Permission]

Retrieves all active users (staff and regular) with optional filtering and search capabilities.

**Query Parameters:**
- `search` - Search by email
- `is_staff` - Filter by staff status (true/false)

```json
No request body required
```

---

### **23. Get User Details**

**Available requests:** [GET]

**GET:** `/api/staff/users/{user_id}/` [Staff Users with User Management Permission]

Retrieves detailed information about a specific user including groups, permissions, and staff profile.

**Response Example:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "is_staff": true,
  "is_superuser": false,
  "groups": ["Staff Manager (SM)"],
  "permissions": ["auth.add_user", "auth.change_user"],
  "staff_profile": {
    "employee_id": "EMP001",
    "department": "ADMIN",
    "position_title": "System Administrator"
  }
}
```

---

### **24. Get User Groups**

**Available requests:** [GET]

**GET:** `/api/staff/users/{user_id}/groups/` [Staff Users with User Management Permission]

Retrieves all group memberships for a specific user with assignment details.

**Response Example:**
```json
{
  "success": true,
  "groups": [
    {
      "id": 1,
      "user": {"email": "<EMAIL>"},
      "group": {"name": "Staff Manager (SM)"},
      "assigned_by": {"email": "<EMAIL>"},
      "assigned_at": "2024-01-15T10:30:00Z",
      "is_active": true,
      "notes": "Initial assignment"
    }
  ]
}
```

---

### **25. Toggle User Staff Status**

**Available requests:** [POST]

**POST:** `/api/staff/users/{user_id}/toggle_staff/` [Superusers Only]

Toggles the staff status of a user (promotes/demotes staff access) with audit logging.

```json
No request body required
```

**Response Example:**
```json
{
  "success": true,
  "message": "User <EMAIL> staff status set to true",
  "is_staff": true
}
```

---

## **Permission Management Endpoints**

### **26. List All Permissions**

**Available requests:** [GET]

**GET:** `/api/staff/permissions/` [Staff Users]

Retrieves all available permissions organized by app and model for group assignment.

**Query Parameters:**
- `search` - Search by permission name or codename
- `content_type` - Filter by content type ID

**Response Example:**
```json
{
  "success": true,
  "total_permissions": 45,
  "grouped_permissions": {
    "auth": [
      {
        "id": 1,
        "name": "Can add user",
        "codename": "add_user",
        "app_label": "auth",
        "model": "user"
      }
    ],
    "core": [
      {
        "id": 25,
        "name": "Can toggle staff status for users",
        "codename": "can_toggle_staff_status",
        "app_label": "core",
        "model": "user"
      }
    ]
  }
}
```

---

## **Audit & Monitoring Endpoints**

### **27. List Audit Logs**

**Available requests:** [GET]

**GET:** `/api/staff/audit/` [Staff Users with Audit Access Permission]

Retrieves audit logs with comprehensive filtering capabilities.

**Query Parameters:**
- `action` - Filter by action type (e.g., 'user_added_to_group')
- `performed_by` - Filter by user ID who performed the action
- `start_date` - Filter from date (YYYY-MM-DD)
- `end_date` - Filter to date (YYYY-MM-DD)
- `target_user` - Filter by target user ID
- `target_group` - Filter by target group ID

**Response Example:**
```json
{
  "count": 25,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "action": "user_added_to_group",
      "performed_by_email": "<EMAIL>",
      "target_user_email": "<EMAIL>",
      "target_group_name": "Staff Manager (SM)",
      "details": {"notes": "Promoted to manager"},
      "ip_address": "*************",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

### **28. Get Audit Summary**

**Available requests:** [GET]

**GET:** `/api/staff/audit/summary/` [Staff Users with Audit Access Permission]

Retrieves audit summary statistics for different time periods.

**Response Example:**
```json
{
  "success": true,
  "summary": {
    "last_24_hours": {
      "total_actions": 15,
      "action_breakdown": [
        {"action": "user_added_to_group", "count": 8},
        {"action": "staff_profile_updated", "count": 4}
      ],
      "top_users": [
        {"performed_by__email": "<EMAIL>", "count": 10}
      ]
    },
    "last_7_days": {
      "total_actions": 45,
      "action_breakdown": [...],
      "top_users": [...]
    },
    "last_30_days": {
      "total_actions": 120,
      "action_breakdown": [...],
      "top_users": [...]
    }
  },
  "generated_at": "2024-01-15T15:30:00Z"
}
```

---

## **Authentication Endpoints (Core App)**

**Note:** Authentication is handled by the core app, not the staff app. These endpoints are available at `/auth/` base URL.

### **29. User Login**

**Available requests:** [POST]

**POST:** `/auth/users/login/` [All Users]

Authenticates users (including staff) and returns JWT tokens for API access.

```json
{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**Response Example:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "is_staff": true
  }
}
```

---

### **30. User Logout**

**Available requests:** [POST]

**POST:** `/auth/users/logout/` [Authenticated Users]

Logs out the current user and invalidates the refresh token.

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

### **31. Get Current User (Core)**

**Available requests:** [GET]

**POST:** `/auth/users/me/` [Authenticated Users]

Gets current user information from the core app (different from staff-specific endpoint).

```json
No request body required
```

---

### **32. JWT Token Refresh**

**Available requests:** [POST]

**POST:** `/auth/jwt/refresh/` [All Users]

Refreshes the access token using a valid refresh token.

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

### **33. JWT Token Verify**

**Available requests:** [POST]

**POST:** `/auth/jwt/verify/` [All Users]

Verifies if a JWT token is valid.

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

## **Summary**

### **Total Endpoints Available: 33**

**By Category:**
- **Authentication & User Info**: 3 endpoints
- **Role Management**: 6 endpoints
- **Staff Profile Management**: 5 endpoints
- **Staff User Creation**: 1 endpoint
- **Group Management**: 6 endpoints
- **User Management**: 4 endpoints
- **Permission Management**: 1 endpoint
- **Audit & Monitoring**: 2 endpoints
- **Core Authentication**: 5 endpoints

### **Permission Requirements:**

**Staff Users (Basic)**: Can access user info, view roles/groups, view permissions
**Group Management Permission**: Can create/modify groups and assign users
**Staff Management Permission**: Can manage staff profiles and organizational structure
**Audit Access Permission**: Can view audit logs and summaries
**Superuser Only**: Can toggle staff status and perform system-level operations

### **Key Features:**

- ✅ **Complete RBAC Implementation**: All authorization features are functional
- ✅ **Comprehensive Audit Logging**: All actions are tracked with detailed metadata
- ✅ **Flexible Permission System**: Dynamic permission checking with custom permissions
- ✅ **Organizational Hierarchy**: Manager-subordinate relationships with validation
- ✅ **Bulk Operations**: Efficient bulk user assignment to groups
- ✅ **Advanced Filtering**: Search and filter capabilities across all endpoints

### **Implementation Status:**

- ✅ **Staff Authorization/RBAC**: Fully implemented and operational
- 🚧 **Product Management**: Planned (endpoints reserved but not implemented)
- 🚧 **Order Management**: Planned (endpoints reserved but not implemented)
- 🚧 **Customer Management**: Planned (endpoints reserved but not implemented)

---

**Document Version**: v3.0
**Last Updated**: July 2025
**Total Documented Endpoints**: 33 (All Verified & Functional)
