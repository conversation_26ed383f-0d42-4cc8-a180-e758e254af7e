# Comprehensive User Guide: Packing & Shipping Service

## 📋 Overview

This guide provides comprehensive documentation for the packing and shipping service, covering usage for administrators, staff members, and developers.

## 👥 Target Audience

- **Store Administrators**: Configure shipping boxes, carriers, and packing rules
- **Staff Members**: Monitor shipping operations and troubleshoot issues
- **Developers**: Understand the system architecture and extend functionality
- **API Users**: Integrate with the shipping calculation endpoints

## 🏗️ System Architecture

### Core Components

1. **Packing Service**: Calculates optimal packaging using 3D bin packing
2. **Shipping Service**: Manages carrier integrations and rate calculations
3. **Cart Integration**: Real-time shipping calculation during cart operations
4. **Admin Interface**: Configuration and monitoring tools

### Data Models

- **Box**: Shipping container specifications
- **Carrier**: Shipping provider configurations
- **CarrierService**: Specific services offered by carriers
- **PackingRule**: Configurable business rules for packaging

## 🔧 Administrator Guide

### Initial Setup

1. **Populate Initial Data**

   ```bash
   python manage.py populate_shipping_data
   ```

   This creates standard boxes, carriers, and basic packing rules.

2. **Configure Carriers**
   - Navigate to Django Admin → Shipping → Carriers
   - Add API credentials for live carriers
   - Set priority and weight limits
   - Configure services for each carrier

3. **Set Up Boxes**
   - Navigate to Django Admin → Shipping → Boxes
   - Define internal dimensions in centimeters
   - Set maximum weight capacity in grams
   - Configure costs in USD
   - Set priorities for selection preference

### Box Management

#### Adding New Boxes

1. Go to Admin → Shipping → Boxes → Add Box
2. Fill in required fields:
   - **Title**: Descriptive name (e.g., "Medium Box")
   - **Internal Dimensions**: Length × Width × Height in cm
   - **Max Weight**: Capacity in grams
   - **Cost**: Box cost in USD
   - **Is Mailer**: Check if it's a padded envelope
   - **Priority**: Higher numbers = preferred selection

#### Box Selection Logic

- System selects boxes based on:
  1. Item dimensions fit within box
  2. Total weight within box capacity
  3. Cost efficiency (cost per volume)
  4. Priority settings

### Carrier Configuration

#### Setting Up Posten Bring

1. Create carrier with code: `posten_bring`
2. Configure services:
   - Standard Parcel (3-5 days)
   - Express Parcel (1-2 days)
3. Set cost multipliers for different service levels

#### Adding New Carriers

1. Create carrier record with unique code
2. Implement carrier class in `apps/shipping/services/carriers/`
3. Add carrier mapping in `get_carrier_instance()`
4. Configure API credentials and services

### Packing Rules

#### Rule Types

1. **Weight-based**: Apply to items within weight ranges
2. **Volume-based**: Apply to items within volume ranges
3. **Item count**: Apply to orders with specific item quantities
4. **Product type**: Apply to specific product categories

#### Creating Rules

1. Go to Admin → Shipping → Packing Rules → Add Rule
2. Set conditions (all optional):
   - Weight range (min/max in grams)
   - Volume range (min/max in cm³)
   - Item count range
   - Product types
3. Define actions:
   - Preferred box selection
   - Force mailer usage
   - Force separate packaging
   - Additional costs
   - Cost multipliers

#### Rule Priority

- Higher priority rules are applied first
- Rules are processed in descending priority order
- Items matching rules are removed from general packing

### Monitoring & Analytics

#### Shipping Performance

- Monitor average shipping costs
- Track box utilization rates
- Analyze carrier performance
- Review packing efficiency

#### Common Issues

1. **High shipping costs**: Review box selection and carrier rates
2. **Poor utilization**: Adjust box sizes or packing rules
3. **Carrier failures**: Check API credentials and connectivity

## 👨‍💼 Staff Guide

### Daily Operations

#### Monitoring Shipping Calculations

1. Check cart shipping calculations are working
2. Monitor for calculation errors in logs
3. Verify carrier API connectivity

#### Troubleshooting

##### Cart Shipping Issues

1. **Shipping not calculating**:
   - Check if customer has shipping address
   - Verify product dimensions are set
   - Check for active carriers

2. **Incorrect shipping costs**:
   - Review packing rules
   - Check box configurations
   - Verify carrier rate calculations

3. **Performance issues**:
   - Check cache status via API
   - Clear shipping cache if needed
   - Monitor calculation times

##### API Endpoints for Troubleshooting

```
GET /api/shipping/calculate/cache_status/
POST /api/shipping/calculate/clear_cache/
POST /api/shipping/carriers/{id}/test_connection/
```

### Order Processing

#### Shipping Information

- Orders include calculated packing and shipping costs
- Packing details show selected boxes and utilization
- Tracking information available for supported carriers

#### Manual Overrides

- Staff can manually adjust shipping costs if needed
- Document reasons for manual adjustments
- Review patterns for rule improvements

## 💻 Developer Guide

### Architecture Overview

#### Service Layer

```
CartShippingService
├── PackingService (3D bin packing)
├── ShippingService (carrier management)
└── Carrier implementations
```

#### Key Classes

- `PackingService`: Handles optimal packaging calculation
- `ShippingService`: Manages carrier integrations
- `CartShippingService`: Coordinates cart shipping updates
- `BaseCarrier`: Abstract carrier interface

### API Integration

#### Calculate Shipping for Cart

```http
POST /api/shipping/calculate/
{
  "cart_id": "uuid",
  "force_recalculate": false
}
```

#### Get Shipping Options

```http
POST /api/shipping/options/
{
  "cart_id": "uuid"
}
```

#### Test Packing Algorithm

```http
POST /api/shipping/test_packing/
{
  "test_items": [
    {
      "length": 10.0,
      "width": 8.0,
      "height": 5.0,
      "weight": 200.0,
      "quantity": 2
    }
  ]
}
```

### Extending the System

#### Adding New Carriers

1. Create carrier class inheriting from `BaseCarrier`
2. Implement required methods:
   - `get_shipping_rate()`
   - `create_shipment()`
   - `track_shipment()`
3. Add to carrier factory in `get_carrier_instance()`

#### Custom Packing Rules

1. Extend `PackingRule` model if needed
2. Modify `PackingService._apply_packing_rules()`
3. Update admin interface for new fields

#### Performance Optimization

1. Monitor cache hit rates
2. Optimize database queries
3. Consider async processing for complex calculations

### Testing

#### Unit Tests

```python
from apps.shipping.services import PackingService

def test_packing_calculation():
    service = PackingService()
    # Test with mock cart items
    result = service.calculate_optimal_packaging(mock_items)
    assert result.success
```

#### Integration Tests

```python
def test_cart_shipping_integration():
    # Test full cart shipping workflow
    cart = create_test_cart()
    service = CartShippingService()
    result = service.recalculate_cart_shipping(cart)
    assert result['success']
```

## 🔍 Troubleshooting Guide

### Common Issues

#### 1. Shipping Not Calculating

**Symptoms**: Cart shows $0.00 shipping cost

**Causes & Solutions**:

- No shipping address: Customer must set default address
- No active carriers: Check carrier configuration
- Product missing dimensions: Set length/width/height on variants
- Cache issues: Clear shipping cache

#### 2. High Shipping Costs

**Symptoms**: Customers complain about expensive shipping

**Causes & Solutions**:

- Inefficient box selection: Review box configurations
- Carrier rates too high: Compare carrier options
- Packing rules adding costs: Review rule configurations
- Multiple boxes for small orders: Adjust packing logic

#### 3. Performance Issues

**Symptoms**: Slow cart updates, timeouts

**Causes & Solutions**:

- Complex packing calculations: Optimize item count
- Database query issues: Check query optimization
- Cache misses: Verify cache configuration
- Carrier API delays: Implement timeouts and fallbacks

#### 4. Carrier Integration Failures

**Symptoms**: Fallback shipping rates always used

**Causes & Solutions**:

- API credentials invalid: Update carrier configuration
- Network connectivity: Check firewall and DNS
- Rate limiting: Implement proper retry logic
- API changes: Update carrier implementation

### Diagnostic Tools

#### Cache Status Check

```bash
curl -X GET /api/shipping/calculate/cache_status/
```

#### Carrier Connection Test

```bash
curl -X POST /api/shipping/carriers/1/test_connection/
```

#### Packing Algorithm Test

```bash
curl -X POST /api/shipping/test_packing/ \
  -d '{"test_items": [{"length": 10, "width": 10, "height": 10, "weight": 100}]}'
```

### Log Analysis

#### Key Log Locations

- Shipping calculations: `apps.shipping.services`
- Cart operations: `apps.cart.serializers`
- Carrier integrations: `apps.shipping.services.carriers`

#### Important Log Messages

- `"Packing calculation failed"`: Check item dimensions
- `"Carrier {name} failed"`: Check carrier configuration
- `"Using fallback shipping rate"`: Investigate carrier issues

## 📊 Best Practices

### Configuration

1. **Start Simple**: Begin with basic boxes and rules
2. **Monitor Performance**: Track calculation times and accuracy
3. **Regular Reviews**: Analyze shipping costs and efficiency
4. **Test Changes**: Use test endpoints before production changes

### Maintenance

1. **Cache Management**: Clear cache after configuration changes
2. **Carrier Updates**: Monitor for API changes and rate updates
3. **Rule Optimization**: Review and refine packing rules regularly
4. **Performance Monitoring**: Track system performance metrics

### Security

1. **API Credentials**: Secure carrier API keys
2. **Access Control**: Limit admin access to shipping configuration
3. **Audit Logs**: Monitor configuration changes
4. **Rate Limiting**: Implement API rate limiting

This comprehensive guide ensures all users can effectively utilize and maintain the packing and shipping service system.
