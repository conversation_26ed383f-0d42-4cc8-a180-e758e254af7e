# Developer Guide: Packing & Shipping Service

## 🎯 Quick Start for New Developers

### Prerequisites

- Python 3.8+
- Django 4.0+
- Redis (for caching)
- py3dbp library for 3D bin packing

### Setup

1. Install dependencies:

   ```bash
   pip install py3dbp django-redis
   ```

2. Run migrations:

   ```bash
   python manage.py makemigrations shipping
   python manage.py migrate
   ```

3. Populate initial data:

   ```bash
   python manage.py populate_shipping_data
   ```

## 🏗️ Architecture Deep Dive

### Service Layer Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Cart Operations                          │
├─────────────────────────────────────────────────────────────┤
│  CartShippingService (Orchestrator)                        │
│  ├── PackingService (3D Bin Packing)                       │
│  ├── ShippingService (Carrier Management)                  │
│  └── Carrier Implementations (Posten Bring, etc.)         │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                               │
│  ├── Box (Shipping containers)                             │
│  ├── Carrier (Shipping providers)                          │
│  ├── PackingRule (Business rules)                          │
│  └── Cache Layer (Redis)                                   │
└─────────────────────────────────────────────────────────────┘
```

### Key Design Patterns

#### 1. Service Layer Pattern

- **Purpose**: Separate business logic from views and models
- **Implementation**: Services in `apps/shipping/services/`
- **Benefits**: Testable, reusable, maintainable

#### 2. Strategy Pattern (Carriers)

- **Purpose**: Interchangeable carrier implementations
- **Implementation**: `BaseCarrier` abstract class with concrete implementations
- **Benefits**: Easy to add new carriers without changing existing code

#### 3. Factory Pattern (Carrier Creation)

- **Purpose**: Create carrier instances based on configuration
- **Implementation**: `get_carrier_instance()` function
- **Benefits**: Centralized carrier instantiation logic

## 🔧 Core Components

### 1. PackingService

**Location**: `apps/shipping/services/packing.py`

**Responsibilities**:

- Calculate optimal packaging using 3D bin packing
- Apply business rules for packaging decisions
- Handle fallback scenarios when optimal packing fails

**Key Methods**:

```python
def calculate_optimal_packaging(self, cart_items) -> PackingResult:
    """Main entry point for packaging calculation"""
    
def _apply_packing_rules(self, items):
    """Apply configured business rules"""
    
def _run_3d_bin_packing(self, items):
    """Execute 3D bin packing algorithm"""
```

**Data Flow**:

1. Filter physical items (exclude digital products)
2. Apply packing rules (high priority items)
3. Run 3D bin packing for remaining items
4. Combine results and return PackingResult

### 2. ShippingService

**Location**: `apps/shipping/services/shipping.py`

**Responsibilities**:

- Manage carrier integrations
- Calculate shipping rates from multiple carriers
- Handle caching and fallback scenarios

**Key Methods**:

```python
def calculate_shipping_cost(self, packing_result, destination_address) -> ShippingRate:
    """Get best shipping rate from available carriers"""
    
def get_all_shipping_rates(self, packing_result, destination_address) -> List[ShippingRate]:
    """Get rates from all carriers for comparison"""
```

### 3. CartShippingService

**Location**: `apps/shipping/services/cart.py`

**Responsibilities**:

- Orchestrate cart shipping calculations
- Handle cart item operations (add/remove/update)
- Manage shipping recalculation triggers

**Key Methods**:

```python
def recalculate_cart_shipping(self, cart, force_recalculate=False):
    """Main method for cart shipping calculation"""
    
def add_item_and_recalculate(self, cart, product_variant, quantity):
    """Add item and trigger shipping recalculation"""
```

## 🔌 Carrier Integration

### Creating a New Carrier

1. **Create Carrier Class**:

   ```python
   # apps/shipping/services/carriers/my_carrier.py
   from .base import BaseCarrier
   
   class MyCarrier(BaseCarrier):
       def get_shipping_rate(self, packing_result, destination_address):
           # Implement rate calculation
           pass
       
       def create_shipment(self, order, packing_result):
           # Implement shipment creation
           pass
       
       def track_shipment(self, tracking_number):
           # Implement tracking
           pass
   ```

2. **Register in Factory**:

   ```python
   # apps/shipping/services/carriers/__init__.py
   def get_carrier_instance(carrier_config):
       carrier_map = {
           'my_carrier': MyCarrier,
           # ... existing carriers
       }
   ```

3. **Create Database Record**:

   ```python
   carrier = Carrier.objects.create(
       title="My Carrier",
       code="my_carrier",
       is_active=True
   )
   ```

### Carrier Interface Requirements

All carriers must implement the `BaseCarrier` interface:

```python
class BaseCarrier(ABC):
    @abstractmethod
    def get_shipping_rate(self, packing_result, destination_address) -> ShippingRate:
        """Calculate shipping rate"""
        
    @abstractmethod
    def create_shipment(self, order, packing_result) -> Dict[str, Any]:
        """Create shipment and return tracking info"""
        
    @abstractmethod
    def track_shipment(self, tracking_number) -> Dict[str, Any]:
        """Get shipment tracking status"""
```

## 📊 Data Models

### Box Model

```python
class Box(models.Model):
    title = models.CharField(max_length=100)
    internal_length = models.DecimalField(max_digits=8, decimal_places=2)  # cm
    internal_width = models.DecimalField(max_digits=8, decimal_places=2)   # cm
    internal_height = models.DecimalField(max_digits=8, decimal_places=2)  # cm
    max_weight = models.DecimalField(max_digits=8, decimal_places=2)       # grams
    cost = models.DecimalField(max_digits=6, decimal_places=2)             # USD
    volume = models.DecimalField(max_digits=12, decimal_places=4)          # computed
    is_mailer = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)
```

### PackingRule Model

```python
class PackingRule(models.Model):
    title = models.CharField(max_length=100)
    priority = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    
    # Conditions (all optional)
    min_weight = models.DecimalField(...)
    max_weight = models.DecimalField(...)
    min_volume = models.DecimalField(...)
    max_volume = models.DecimalField(...)
    product_types = models.ManyToManyField('products.ProductType')
    
    # Actions
    preferred_box = models.ForeignKey(Box, ...)
    force_mailer = models.BooleanField(default=False)
    additional_cost = models.DecimalField(...)
    cost_multiplier = models.DecimalField(...)
```

## 🔄 Integration Points

### Cart Integration

The shipping service integrates with the cart system at several points:

1. **Item Addition**: Triggers shipping recalculation
2. **Item Removal**: Triggers shipping recalculation  
3. **Quantity Update**: Triggers shipping recalculation
4. **Address Change**: Triggers shipping recalculation

**Implementation**:

```python
# In cart serializers
def save(self, **kwargs):
    instance = super().save(**kwargs)
    self._recalculate_shipping(instance.cart)
    return instance

def _recalculate_shipping(self, cart):
    from apps.shipping.services import CartShippingService
    service = CartShippingService()
    service.recalculate_cart_shipping(cart, force_recalculate=True)
```

### Order Integration

When orders are created from carts, shipping information is preserved:

```python
# Order creation
order.packing_cost = cart.packing_cost
order.shipping_cost = cart.shipping_cost
order.total_weight = cart.total_weight
```

## 🧪 Testing Strategy

### Unit Tests

**Test PackingService**:

```python
def test_packing_service():
    service = PackingService()
    mock_items = create_mock_cart_items()
    result = service.calculate_optimal_packaging(mock_items)
    
    assert result.success
    assert len(result.boxes) > 0
    assert result.total_cost > 0
```

**Test ShippingService**:

```python
def test_shipping_service():
    service = ShippingService()
    mock_packing_result = create_mock_packing_result()
    mock_address = create_mock_address()
    
    rate = service.calculate_shipping_cost(mock_packing_result, mock_address)
    
    assert rate is not None
    assert rate.cost > 0
```

### Integration Tests

**Test Cart Integration**:

```python
def test_cart_shipping_integration():
    cart = create_test_cart()
    add_items_to_cart(cart)
    
    # Shipping should be calculated automatically
    cart.refresh_from_db()
    assert cart.shipping_cost > 0
    assert cart.packing_cost > 0
```

### Mock Data Creation

```python
def create_mock_cart_items():
    MockCartItem = namedtuple('MockCartItem', ['product', 'product_variant', 'quantity'])
    MockProduct = namedtuple('MockProduct', ['is_digital', 'title'])
    MockVariant = namedtuple('MockVariant', ['weight', 'length', 'width', 'height', 'volume', 'sku'])
    
    items = []
    for i in range(3):
        product = MockProduct(is_digital=False, title=f"Product {i}")
        variant = MockVariant(
            weight=100 * (i + 1),
            length=10, width=10, height=10,
            volume=1000, sku=f"SKU-{i}"
        )
        item = MockCartItem(product=product, product_variant=variant, quantity=1)
        items.append(item)
    
    return items
```

## 🚀 Performance Optimization

### Caching Strategy

**Cache Keys**:

```python
CACHE_KEYS = {
    'boxes': 'shipping:packing:boxes',
    'rules': 'shipping:rules:active',
    'carriers': 'shipping:carriers:active',
    'rates': 'shipping:rates:{hash}'
}
```

**Cache Invalidation**:

- Box changes: Clear box cache
- Rule changes: Clear rules cache
- Carrier changes: Clear carrier cache
- Rate calculations: TTL-based expiration

### Database Optimization

**Query Optimization**:

```python
# Efficient cart item loading
cart_items = cart.cart_items.select_related(
    'product', 'product_variant', 'product__product_type'
).all()

# Prefetch related data
boxes = Box.objects.filter(is_active=True).order_by('volume', 'cost')
```

**Indexes**:

- `ProductVariant`: volume, weight, dimensions
- `Box`: volume, cost, is_active
- `PackingRule`: priority, is_active

## 🔍 Debugging & Monitoring

### Logging

**Key Log Points**:

```python
logger.info(f"Packing calculation started for {len(items)} items")
logger.warning(f"Carrier {carrier.name} failed: {error}")
logger.error(f"Shipping calculation failed: {exception}")
```

**Log Analysis**:

- Monitor calculation times
- Track carrier failure rates
- Identify performance bottlenecks

### Monitoring Endpoints

**Cache Status**:

```http
GET /api/shipping/calculate/cache_status/
```

**Carrier Health**:

```http
GET /api/shipping/carriers/status/
```

**Performance Metrics**:

- Average calculation time
- Cache hit rates
- Carrier success rates

## 🔧 Common Development Tasks

### Adding New Box Types

1. Create box in admin or via API
2. Test with packing algorithm
3. Adjust priority if needed
4. Monitor utilization rates

### Modifying Packing Rules

1. Update rule conditions/actions
2. Clear rules cache
3. Test with sample data
4. Monitor impact on costs

### Carrier Maintenance

1. Update API credentials
2. Test connection
3. Monitor rate accuracy
4. Handle API changes

### Performance Tuning

1. Profile calculation times
2. Optimize database queries
3. Adjust cache timeouts
4. Monitor memory usage

This guide provides the foundation for developers to understand, maintain, and extend the packing and shipping service system.
