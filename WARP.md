# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

This is "Picky PC" - a Django REST Framework-based e-commerce application for PC hardware components. The application is built with a modular architecture using Django apps and implements a comprehensive role-based access control (RBAC) system for staff management.

## Architecture Overview

### Core Django Apps Structure

- **`apps/core`** - User authentication, custom user model, and shared utilities
- **`apps/products`** - Product catalog with variants, categories, brands, and attributes
- **`apps/customers`** - Customer profiles and address management
- **`apps/cart`** - Shopping cart functionality
- **`apps/order`** - Order processing and management (customer-facing)
- **`apps/payments`** - Payment integration (Stripe, PayPal via Braintree)
- **`apps/wishlist`** - User wishlist functionality
- **`apps/shipping`** - Shipping and packing management
- **`apps/staff`** - Staff management with RBAC system (admin-facing)

### Key Design Patterns

**Dual API Architecture**: Customer and staff operations are cleanly separated:
- Customer APIs: Simple CRUD operations under `/api/` prefixes
- Staff APIs: Advanced operations with role-based permissions under `/api/staff/`

**RBAC System**: The staff app implements a sophisticated Role-Based Access Control system using Django's Group and Permission models with custom extensions.

**Product Variant System**: Complex product modeling with:
- Products with multiple variants (size, color, etc.)
- Attribute-based filtering and search
- MPTT categories for hierarchical organization

### Database Architecture

The application uses PostgreSQL as the primary database with key relationships:
- Custom User model supporting email/phone authentication
- MPTT for category hierarchies
- Many-to-many relationships for product attributes
- Order system with detailed item tracking

## Common Development Commands

### Local Development Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Create and run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic

# Run development server
python manage.py runserver
```

### Docker Development

```bash
# Start services (PostgreSQL, Redis, Django)
docker-compose up

# Build and start in detached mode
docker-compose up -d --build

# View logs
docker-compose logs -f web

# Stop services
docker-compose down
```

### Database Operations

```bash
# Reset database (development only)
python manage.py flush

# Create migrations for specific app
python manage.py makemigrations products

# Show migration status
python manage.py showmigrations

# SQL for specific migration
python manage.py sqlmigrate products 0001

# Load fixture data
python manage.py loaddata fixtures/initial_data.json
```

### Testing

```bash
# Run all tests
pytest

# Run tests for specific app
pytest apps/products/tests.py

# Run with coverage
pytest --cov=apps

# Run specific test
pytest apps/products/tests.py::TestProductModel::test_product_creation
```

### Staff RBAC Management

```bash
# Setup RBAC system (run once)
python manage.py setup_rbac

# Assign role to user
python manage.py assign_role <EMAIL> SuperAdmin

# Assign role with context
python manage.py assign_role <EMAIL> ProductManager \
  --context '{"brand_access": [1, 2, 3]}'
```

### Celery (Background Tasks)

```bash
# Start Celery worker (in separate terminal)
celery -A pc_hardware worker --loglevel=info

# Start Celery beat scheduler
celery -A pc_hardware beat --loglevel=info

# Monitor tasks
celery -A pc_hardware flower
```

## Settings Configuration

The application uses environment-based settings:

- **Development**: `pc_hardware.settings.dev` (default)
- **Production**: `pc_hardware.settings.production`

### Key Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Redis
REDIS_URL=redis://localhost:6379/0

# Cloudinary (for media storage)
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Social Auth
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY=your_google_key
GOOGLE_OAUTH2_SECRET=your_google_secret

# Stripe
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

## API Documentation

- **Swagger UI**: `/api/schema/doc/`
- **ReDoc**: `/api/schema/re-doc/`
- **OpenAPI Schema**: `/api/schema/`

## Development Patterns & Conventions

### ViewSets and Serializers
- Use `ModelViewSet` for full CRUD operations
- Implement custom actions using `@action` decorator
- Separate serializers for different use cases (list, detail, create, update)
- Use `SerializerMethodField` for computed values

### Permissions
- Customer operations use `IsCustomerOwner` for object-level permissions
- Staff operations use role-based permissions with `IsStaffUser` base class
- Complex permissions implemented through custom permission classes

### Model Patterns
- Use `IsActiveQuerySet` for soft deletes via `is_active` field
- Implement `__str__` methods for all models
- Use `related_name` consistently for reverse relationships
- Add `db_index=True` for frequently queried fields

### Error Handling
- Consistent error responses using DRF's exception handling
- Custom validation in serializers using `validate()` and `validate_<field>()` methods
- Proper HTTP status codes for different scenarios

## Production Considerations

### Deployment Stack
- **Web Server**: Gunicorn with WhiteNoise for static files
- **Database**: PostgreSQL with SSL required
- **Cache**: Redis (or LocMem for lightweight deployments)
- **Media Storage**: Cloudinary
- **Container**: Docker with multi-stage builds

### Security Features
- JWT authentication with HTTP-only cookies
- CORS configuration for frontend integration
- CSRF protection with trusted origins
- SSL/TLS enforcement in production
- Social authentication integration

### Performance Optimizations
- Database query optimization with `select_related` and `prefetch_related`
- Caching strategy for frequently accessed data
- Pagination for large datasets
- Background task processing with Celery

## Staff Administration

The staff app provides a comprehensive admin interface with:
- Role-based access control with granular permissions
- Audit logging for all administrative actions
- Bulk operations for data management
- Contextual filtering based on user roles
- Advanced user and group management

## Troubleshooting

### Common Issues

**Migration Conflicts**: Use `python manage.py makemigrations --merge` to resolve
**Static Files**: Ensure `STATIC_ROOT` is properly configured and run `collectstatic`
**Database Connection**: Check `DATABASE_URL` environment variable format
**CORS Issues**: Verify `CORS_ALLOWED_ORIGINS` includes your frontend URL

### Logging

Development logs are written to:
- Console output (formatted)
- `general.log` file (detailed)

Production logs are sent to stdout for container environments.

## Additional Resources

- Django REST Framework documentation for API patterns
- MPTT documentation for category tree management
- Celery documentation for background task implementation
- The codebase includes extensive inline documentation and docstrings
