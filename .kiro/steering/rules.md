---
inclusion: always
---

- Create a task list before starting any implementation (required if 3+ steps).
- Break down complex work into clear, actionable tasks.
- Assume all packages (npm, pip, etc.)  are pre-installed. Do not ask to install packages during tasks. Mentions the required packages to the user after completing all the tasks.  
- Do not attempt to update or write tests/docs after tasks are complete unless the user has requested it..
- Ensure all the listed tasks are complete before wrapping things up.  
- Use available, popular, reputable, and maintained packages (npm, pip, etc.) for completing tasks instead of defining new utility functions for every task.
- Add concise comments to the code to explain the logic and functionality.
- Use meaningful names for variable, function, classes, etc using language specific naming conventions.
- Follow best practices for code organization, indentation, and spacing.
- Add error handling and edge case considerations to the code.
- Use a consistent coding style and formatting throughout the project.
- Do not make any code changes unless the user explicitly asks for it. Look for any words (commands) like 'update', 'change', 'modify', 'make', 'add', 'create', 'implement', etc.
- Always use necessary MCP servers for tasks needing live data, up to date information (like most recent documentations, etc.), external interactions, etc. beyond your base model.
- Terminal commands and testing will be done manually by the user.
- Do not create these specs files when starting every task (requirements.md, design.md, etc.) unless the use asks to create them.
