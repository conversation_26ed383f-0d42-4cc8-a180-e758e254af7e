openapi: 3.0.3
info:
  title: ''
  version: 0.0.0
paths:
  /api/store/brands/:
    get:
      operationId: api_store_brands_list
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Category'
          description: ''
    post:
      operationId: api_store_brands_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Category'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Category'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Category'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
  /api/store/brands/{id}/:
    get:
      operationId: api_store_brands_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this brand.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    put:
      operationId: api_store_brands_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this brand.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Category'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Category'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Category'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    patch:
      operationId: api_store_brands_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this brand.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    delete:
      operationId: api_store_brands_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this brand.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/store/categories/:
    get:
      operationId: api_store_categories_list
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Category'
          description: ''
    post:
      operationId: api_store_categories_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Category'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Category'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Category'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
  /api/store/categories/{id}/:
    get:
      operationId: api_store_categories_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this category.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    put:
      operationId: api_store_categories_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this category.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Category'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Category'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Category'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    patch:
      operationId: api_store_categories_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this category.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCategory'
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    delete:
      operationId: api_store_categories_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this category.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/store/products/:
    get:
      operationId: api_store_products_list
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Product'
          description: ''
    post:
      operationId: api_store_products_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Product'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Product'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Product'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
          description: ''
  /api/store/products/{id}/:
    get:
      operationId: api_store_products_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this product.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
          description: ''
    put:
      operationId: api_store_products_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this product.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Product'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Product'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Product'
        required: true
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
          description: ''
    patch:
      operationId: api_store_products_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this product.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedProduct'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedProduct'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedProduct'
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
          description: ''
    delete:
      operationId: api_store_products_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this product.
        required: true
      tags:
      - api
      security:
      - cookieAuth: []
      - basicAuth: []
      - {}
      responses:
        '204':
          description: No response body
components:
  schemas:
    Category:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        title:
          type: string
          maxLength: 50
        is_active:
          type: boolean
      required:
      - id
      - title
    PatchedCategory:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        title:
          type: string
          maxLength: 50
        is_active:
          type: boolean
    PatchedProduct:
      type: object
      properties:
        title:
          type: string
          maxLength: 100
        slug:
          type: string
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
        brand:
          type: string
        category:
          $ref: '#/components/schemas/Category'
        is_digital:
          type: boolean
        is_active:
          type: boolean
    Product:
      type: object
      properties:
        title:
          type: string
          maxLength: 100
        slug:
          type: string
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
        brand:
          type: string
        category:
          $ref: '#/components/schemas/Category'
        is_digital:
          type: boolean
        is_active:
          type: boolean
      required:
      - brand
      - category
      - slug
      - title
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
