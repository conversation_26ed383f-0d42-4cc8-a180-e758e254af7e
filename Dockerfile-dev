# Stage 1
FROM python:3-alpine AS builder

WORKDIR /app

RUN python3 -m venv venv
ENV VIRTUAL_ENV=/app/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Try to use <PERSON><PERSON><PERSON> in development settings
COPY requirements.txt .
RUN pip install -r requirements.txt

# Stage 2
FROM python:3-alpine AS runner

WORKDIR /app

COPY --from=builder /app/venv venv
COPY . /app/
COPY entrypoint-dev.sh /app/

ENV VIRTUAL_ENV=/app/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"
ENV PORT=8000
ENV SECRET_KEY=${SECRET_KEY}
ENV DJANGO_SETTINGS_MODULE=pc_hardware.settings.dev

# Migrations runs at build time (required to supply the secret key, env)
# RUN python manage.py migrate --noinput

# Make the entrypoint script executable
RUN chmod +x /app/entrypoint-dev.sh

# Expose the port the application will run on
EXPOSE ${PORT}

# Collect static files
# RUN python manage.py collectstatic --noinput

# Migrations will every time the container is starts
# ENTRYPOINT ["/app/entrypoint.sh"] \
ENTRYPOINT ["/bin/ash", "/app/entrypoint-dev.sh"]

#CMD ["gunicorn", "--bind", ":8000", "--workers", "2", "pc_hardware.wsgi:application"]

# other ways to run CMD:
# CMD gunicorn pc_hardware.wsgi:application
# CMD gunicorn --bind :${PORT} --workers 2 pc_hardware.wsgi:application