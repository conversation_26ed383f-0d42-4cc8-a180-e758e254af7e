from decimal import Decimal
import logging
from django.utils import timezone
from django.db import transaction
from django.core.cache import cache
from .packing import PackingService
from .shipping import ShippingService
from apps.cart.models import CartItem


class CartShippingService:
    """Service for managing cart shipping calculations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.packing_service = PackingService()
        self.shipping_service = ShippingService()
        self.cache_timeout = 300  # 5 minutes

    def recalculate_cart_shipping(self, cart, force_recalculate=False, destination_address=None):
        """
        Recalculate shipping costs for a cart

        Args:
            cart: Cart instance
            force_recalculate: Force recalculation even if recently calculated
            destination_address: Optional destination address override

        Returns:
            Dictionary with calculation results
        """

        try:
            # Check if we need to recalculate
            if not force_recalculate and self._is_calculation_recent(cart):
                return {
                    'success': True,
                    'message': 'Using cached calculation',
                    'packing_cost': cart.packing_cost,
                    'shipping_cost': cart.shipping_cost,
                    'total_weight': cart.total_weight,
                    'total_volume': cart.total_volume
                }

            # Get cart items with related data
            cart_items = cart.cart_items.select_related(
                'product', 'product_variant', 'product__product_type'
            ).all()

            if not cart_items:
                return self._clear_cart_shipping(cart)

            # Calculate optimal packaging
            packing_result = self.packing_service.calculate_optimal_packaging(cart_items)

            # Get shipping address (use provided address or get from cart/customer)
            shipping_address = destination_address or self._get_shipping_address(cart)

            # Calculate shipping cost (always calculate, even without address)
            shipping_rate = self.shipping_service.calculate_shipping_cost(
                packing_result, shipping_address
            )

            # Prepare detailed packing information for staff
            packing_details = {
                'boxes': [
                    {
                        'box_id': packed_box.box.id,
                        'box_title': packed_box.box.title,
                        'box_dimensions': {
                            'length': float(packed_box.box.internal_length),
                            'width': float(packed_box.box.internal_width),
                            'height': float(packed_box.box.internal_height)
                        },
                        'box_cost': float(packed_box.total_cost),
                        'box_weight_capacity': float(packed_box.box.max_weight),
                        'utilization_percentage': round(packed_box.utilization, 2),
                        'total_weight': float(packed_box.total_weight),
                        'is_mailer': packed_box.box.is_mailer,
                        'items': [
                            {
                                'sku': item.sku,
                                'product_title': item.product_title,
                                'quantity': item.quantity,
                                'weight': float(item.weight),
                                'volume': float(item.volume),
                                'dimensions': {
                                    'length': float(item.dimensions['length']),
                                    'width': float(item.dimensions['width']),
                                    'height': float(item.dimensions['height'])
                                }
                            }
                            for item in packed_box.items
                        ]
                    }
                    for packed_box in packing_result.boxes
                ],
                'packing_method': packing_result.method_used,
                'calculation_time_seconds': round(packing_result.calculation_time, 3),
                'total_boxes': len(packing_result.boxes),
                'unpacked_items': [
                    {
                        'sku': item.get('sku', 'Unknown'),
                        'reason': item.get('reason', 'Unknown error')
                    }
                    for item in packing_result.unpacked_items
                ],
                'warnings': packing_result.warnings,
                'success': packing_result.success,
                'calculated_at': timezone.now().isoformat()
            }

            # Update cart with new values including detailed packing information
            with transaction.atomic():
                cart.packing_cost = packing_result.total_cost
                cart.shipping_cost = shipping_rate.cost if shipping_rate else Decimal('0.00')
                cart.total_weight = packing_result.total_weight
                cart.total_volume = packing_result.total_volume
                cart.packing_details = packing_details
                cart.last_shipping_calculation = timezone.now()
                cart.save(update_fields=[
                    'packing_cost', 'shipping_cost', 'total_weight',
                    'total_volume', 'packing_details', 'last_shipping_calculation'
                ])

            result = {
                'success': True,
                'message': 'Shipping calculated successfully',
                'packing_cost': cart.packing_cost,
                'shipping_cost': cart.shipping_cost,
                'total_weight': cart.total_weight,
                'total_volume': cart.total_volume,
                'packing_result': {
                    'boxes': len(packing_result.boxes),
                    'method_used': packing_result.method_used,
                    'calculation_time': packing_result.calculation_time,
                    'warnings': packing_result.warnings
                }
            }

            if shipping_rate:
                result['shipping_details'] = {
                    'carrier': shipping_rate.carrier_name,
                    'service': shipping_rate.service_name,
                    'estimated_days': shipping_rate.estimated_days,
                    'tracking_available': shipping_rate.tracking_available
                }

            self.logger.info(f"Cart {cart.id} shipping recalculated: "
                             f"packing=${cart.packing_cost}, shipping=${cart.shipping_cost}")

            return result

        except Exception as e:
            self.logger.error(f"Failed to recalculate shipping for cart {cart.id}: {e}")
            return self._handle_calculation_error(cart, str(e))

    def add_item_and_recalculate(self, cart, product_variant, quantity):
        """Add item to cart and recalculate shipping"""

        try:
            with transaction.atomic():
                # Add or update cart item
                cart_item, created = CartItem.objects.get_or_create(
                    cart=cart,
                    product=product_variant.product,
                    product_variant=product_variant,
                    defaults={'quantity': quantity}
                )

                if not created:
                    cart_item.quantity += quantity
                    cart_item.save()

                # Recalculate shipping
                result = self.recalculate_cart_shipping(cart, force_recalculate=True)
                result['cart_item_id'] = cart_item.id
                result['item_added'] = created
                result['new_quantity'] = cart_item.quantity

                return result

        except Exception as e:
            self.logger.error(f"Failed to add item and recalculate shipping: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to add item and calculate shipping'
            }

    def remove_item_and_recalculate(self, cart, cart_item_id):
        """Remove item from cart and recalculate shipping"""

        try:
            with transaction.atomic():
                cart_item = CartItem.objects.get(id=cart_item_id, cart=cart)
                cart_item.delete()

                # Recalculate shipping
                result = self.recalculate_cart_shipping(cart, force_recalculate=True)
                result['item_removed'] = True
                result['removed_item_id'] = cart_item_id

                return result

        except CartItem.DoesNotExist:
            return {
                'success': False,
                'error': 'Cart item not found',
                'message': 'Item not found in cart'
            }
        except Exception as e:
            self.logger.error(f"Failed to remove item and recalculate shipping: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to remove item and calculate shipping'
            }

    def update_quantity_and_recalculate(self, cart, cart_item_id, new_quantity):
        """Update item quantity and recalculate shipping"""

        try:
            with transaction.atomic():
                cart_item = CartItem.objects.get(id=cart_item_id, cart=cart)
                old_quantity = cart_item.quantity

                if new_quantity <= 0:
                    cart_item.delete()
                    removed = True
                else:
                    cart_item.quantity = new_quantity
                    cart_item.save()
                    removed = False

                # Recalculate shipping
                result = self.recalculate_cart_shipping(cart, force_recalculate=True)
                result['quantity_updated'] = True
                result['old_quantity'] = old_quantity
                result['new_quantity'] = new_quantity if not removed else 0
                result['item_removed'] = removed

                return result

        except CartItem.DoesNotExist:
            return {
                'success': False,
                'error': 'Cart item not found',
                'message': 'Item not found in cart'
            }
        except Exception as e:
            self.logger.error(f"Failed to update quantity and recalculate shipping: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to update quantity and calculate shipping'
            }

    def get_shipping_options(self, cart):
        """Get all available shipping options for cart"""

        try:
            # Get cart items
            cart_items = cart.cart_items.select_related(
                'product', 'product_variant'
            ).all()

            if not cart_items:
                return {
                    'success': True,
                    'options': [],
                    'message': 'No items in cart'
                }

            # Calculate packaging
            packing_result = self.packing_service.calculate_optimal_packaging(cart_items)

            # Get shipping address
            shipping_address = self._get_shipping_address(cart)

            if not shipping_address:
                return {
                    'success': False,
                    'error': 'No shipping address available',
                    'message': 'Please set a shipping address'
                }

            # Get all shipping rates
            shipping_rates = self.shipping_service.get_all_shipping_rates(
                packing_result, shipping_address
            )

            options = []
            for rate in shipping_rates:
                total_cost = packing_result.total_cost + rate.cost
                options.append({
                    'carrier': rate.carrier_name,
                    'service': rate.service_name,
                    'cost': str(rate.cost),
                    'total_cost': str(total_cost),
                    'estimated_days': rate.estimated_days,
                    'max_days': rate.max_days,
                    'tracking_available': rate.tracking_available,
                    'insurance_available': rate.insurance_available
                })

            return {
                'success': True,
                'options': options,
                'packing_cost': str(packing_result.total_cost),
                'message': f'Found {len(options)} shipping options'
            }

        except Exception as e:
            self.logger.error(f"Failed to get shipping options for cart {cart.id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to get shipping options'
            }

    def _is_calculation_recent(self, cart, max_age_minutes=5):
        """Check if shipping calculation is recent enough to skip recalculation"""

        if not cart.last_shipping_calculation:
            return False

        age = timezone.now() - cart.last_shipping_calculation
        return age.total_seconds() < (max_age_minutes * 60)

    def _get_shipping_address(self, cart):
        """Get shipping address for cart"""

        if cart.customer:
            # Try to get default shipping address
            try:
                return cart.customer.addresses.filter(is_default=True).first()
            except:
                return cart.customer.addresses.first() if cart.customer.addresses.exists() else None

        return None

    def _get_fallback_address(self):
        """Get fallback address for anonymous cart shipping calculation"""
        # Create a simple address object for shipping calculation
        # You can customize this based on your business needs
        class FallbackAddress:
            def __init__(self):
                self.country = 'US'  # Default country
                self.postal_code = '10001'  # Default postal code
                self.city_or_village = 'New York'
                self.full_name = 'Default'
        
        return FallbackAddress()

    def _clear_cart_shipping(self, cart):
        """Clear shipping costs for empty cart"""

        with transaction.atomic():
            cart.packing_cost = Decimal('0.00')
            cart.shipping_cost = Decimal('0.00')
            cart.total_weight = Decimal('0.00')
            cart.total_volume = Decimal('0.0000')
            cart.packing_details = {}
            cart.last_shipping_calculation = timezone.now()
            cart.save(update_fields=[
                'packing_cost', 'shipping_cost', 'total_weight',
                'total_volume', 'packing_details', 'last_shipping_calculation'
            ])

        return {
            'success': True,
            'message': 'Cart is empty, shipping costs cleared',
            'packing_cost': Decimal('0.00'),
            'shipping_cost': Decimal('0.00'),
            'total_weight': Decimal('0.00'),
            'total_volume': Decimal('0.0000')
        }

    def _handle_calculation_error(self, cart, error_message):
        """Handle shipping calculation errors with fallback values"""

        try:
            # Use fallback values
            fallback_packing = Decimal('5.00')
            fallback_shipping = Decimal('15.00')

            with transaction.atomic():
                cart.packing_cost = fallback_packing
                cart.shipping_cost = fallback_shipping
                cart.packing_details = {
                    'error': True,
                    'fallback_used': True,
                    'error_message': error_message,
                    'calculated_at': timezone.now().isoformat()
                }
                cart.last_shipping_calculation = timezone.now()
                cart.save(update_fields=[
                    'packing_cost', 'shipping_cost', 'packing_details', 'last_shipping_calculation'
                ])

            return {
                'success': False,
                'error': error_message,
                'message': 'Using fallback shipping costs',
                'packing_cost': fallback_packing,
                'shipping_cost': fallback_shipping,
                'fallback_used': True
            }

        except Exception as e:
            self.logger.error(f"Failed to set fallback shipping costs: {e}")
            return {
                'success': False,
                'error': f"Calculation failed: {error_message}. Fallback also failed: {e}",
                'message': 'Shipping calculation completely failed'
            }
