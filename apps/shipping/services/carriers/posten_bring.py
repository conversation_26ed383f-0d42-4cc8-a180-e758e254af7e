from decimal import Decimal
from typing import Optional, Dict, Any
import random
import string
from django.utils import timezone
from .base import BaseCarrier
from ..shipping import ShippingRate
from ..packing import PackingResult


class PostenBringCarrier(BaseCarrier):
    """
    Posten Bring (Norway) carrier implementation with mock data
    
    This implementation uses mock data since we don't have access to the real API.
    Based on 2024 Posten Bring rates converted to USD.
    """

    def __init__(self, config):
        super().__init__(config)
        self.mock_rates = self._load_mock_rates()
        self.base_url = "https://api.bring.com/shippingguide/v2"

    def get_shipping_rate(self, packing_result: PackingResult,
                          destination_address) -> Optional[ShippingRate]:
        """Calculate shipping rate using mock Posten Bring data"""

        try:
            total_weight = float(packing_result.total_weight)
            destination_country = getattr(destination_address, 'country', 'NO').upper()

            # Calculate base rate using mock data
            base_rate = self._calculate_mock_base_rate(total_weight)

            # Apply distance modifier
            distance_modifier = self._get_distance_modifier(destination_country)

            # Apply box count modifier (handling fee per box)
            box_count = len(packing_result.boxes)
            box_modifier = box_count * Decimal('2.50')  # $2.50 per additional box

            # Calculate final cost
            total_cost = base_rate + distance_modifier + box_modifier + self.config.base_cost

            # Get estimated delivery days
            estimated_days = self._estimate_delivery_days(destination_country)
            max_days = estimated_days + 2

            return ShippingRate(
                carrier_name=self.config.title,
                service_name="Standard Parcel",
                cost=total_cost,
                estimated_days=estimated_days,
                max_days=max_days,
                tracking_available=True,
                insurance_available=True,
                signature_available=False,
                carrier_id=self.config.id,
                service_id=None
            )

        except Exception as e:
            self.logger.error(f"Rate calculation failed: {e}")
            return None

    def create_shipment(self, order, packing_result: PackingResult) -> Dict[str, Any]:
        """Create mock shipment (would integrate with real API in production)"""

        try:
            # Generate mock tracking number
            tracking_number = self._generate_mock_tracking_number()

            # Mock shipment creation
            shipment_data = {
                'success': True,
                'tracking_number': tracking_number,
                'carrier': self.config.title,
                'service': 'Standard Parcel',
                'estimated_delivery': self._get_estimated_delivery_date(order.selected_address.country),
                'cost': str(packing_result.total_cost),
                'label_url': f"https://mock-api.bring.com/labels/{tracking_number}.pdf",
                'created_at': timezone.now().isoformat()
            }

            self.logger.info(f"Mock shipment created: {tracking_number}")
            return shipment_data

        except Exception as e:
            self.logger.error(f"Shipment creation failed: {e}")
            return self.format_error_response(f"Failed to create shipment: {e}")

    def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """Track shipment using mock data"""

        try:
            # Mock tracking statuses
            statuses = [
                'Label Created',
                'Picked Up',
                'In Transit',
                'Out for Delivery',
                'Delivered'
            ]

            # Generate mock tracking info
            current_status = random.choice(statuses[:3])  # Don't always show delivered

            tracking_data = {
                'success': True,
                'tracking_number': tracking_number,
                'status': current_status,
                'estimated_delivery': self._get_estimated_delivery_date('NO'),
                'events': [
                    {
                        'timestamp': (timezone.now() - timezone.timedelta(days=1)).isoformat(),
                        'status': 'Label Created',
                        'location': 'Oslo, Norway'
                    },
                    {
                        'timestamp': timezone.now().isoformat(),
                        'status': current_status,
                        'location': 'Oslo Distribution Center'
                    }
                ]
            }

            return tracking_data

        except Exception as e:
            self.logger.error(f"Tracking failed: {e}")
            return self.format_error_response(f"Failed to track shipment: {e}")

    def test_connection(self) -> bool:
        """Test connection to Posten Bring API (mock)"""
        try:
            # Mock API test - in real implementation would ping actual API
            self.logger.info("Testing Posten Bring API connection (mock)")
            return True
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    def _load_mock_rates(self):
        """Load mock rate data based on 2024 Posten Bring rates"""
        return {
            'domestic_norway': [
                (250, Decimal('8.50')),  # Up to 250g - 89 NOK ≈ $8.50 USD
                (500, Decimal('10.50')),  # Up to 500g - 109 NOK ≈ $10.50 USD
                (1000, Decimal('12.50')),  # Up to 1kg - 129 NOK ≈ $12.50 USD
                (2000, Decimal('15.50')),  # Up to 2kg - 159 NOK ≈ $15.50 USD
                (5000, Decimal('19.50')),  # Up to 5kg - 199 NOK ≈ $19.50 USD
                (10000, Decimal('25.50')),  # Up to 10kg - 259 NOK ≈ $25.50 USD
                (20000, Decimal('34.50')),  # Up to 20kg - 349 NOK ≈ $34.50 USD
            ],
            'nordic_countries': {
                'multiplier': Decimal('1.2'),  # 20% increase for Nordic countries
                'base_fee': Decimal('5.00')
            },
            'europe': {
                'multiplier': Decimal('1.5'),  # 50% increase for Europe
                'base_fee': Decimal('12.00')
            },
            'international': {
                'multiplier': Decimal('2.0'),  # 100% increase for international
                'base_fee': Decimal('25.00')
            }
        }

    def _calculate_mock_base_rate(self, weight_grams: float) -> Decimal:
        """Calculate base rate using mock Norwegian postal rates"""

        domestic_rates = self.mock_rates['domestic_norway']

        # Find appropriate weight bracket
        for max_weight, cost in domestic_rates:
            if weight_grams <= max_weight:
                return cost

        # Over 20kg - calculate per kg
        excess_kg = (weight_grams - 20000) / 1000
        base_cost = domestic_rates[-1][1]  # 20kg rate
        additional_cost = excess_kg * Decimal('2.50')  # $2.50 per additional kg

        return base_cost + additional_cost

    def _get_distance_modifier(self, destination_country: str) -> Decimal:
        """Get distance-based pricing modifier"""

        country = destination_country.upper()

        if country == 'NO':
            return Decimal('0.00')  # Domestic Norway
        elif country in ['SE', 'DK', 'FI']:
            # Nordic countries
            return self.mock_rates['nordic_countries']['base_fee']
        elif country in ['DE', 'NL', 'BE', 'FR', 'GB', 'AT', 'CH', 'IT', 'ES']:
            # Western Europe
            return self.mock_rates['europe']['base_fee']
        else:
            # International
            return self.mock_rates['international']['base_fee']

    def _estimate_delivery_days(self, destination_country: str) -> int:
        """Estimate delivery time based on destination"""

        country = destination_country.upper()

        if country == 'NO':
            return 2  # Domestic Norway
        elif country in ['SE', 'DK', 'FI']:
            return 3  # Nordic countries
        elif country in ['DE', 'NL', 'BE', 'FR', 'GB']:
            return 5  # Western Europe
        elif country in ['AT', 'CH', 'IT', 'ES', 'PL', 'CZ']:
            return 7  # Central/Southern Europe
        else:
            return 10  # International

    def _generate_mock_tracking_number(self) -> str:
        """Generate mock tracking number in Posten Bring format"""
        # Posten Bring tracking numbers are typically 13 digits
        prefix = "MOCK"
        suffix = ''.join(random.choices(string.digits, k=9))
        return f"{prefix}{suffix}"

    def _get_estimated_delivery_date(self, destination_country: str):
        """Get the estimated delivery date"""
        days = self._estimate_delivery_days(destination_country)
        return (timezone.now() + timezone.timedelta(days=days)).date().isoformat()

    def validate_address(self, address) -> Dict[str, Any]:
        """Validate Norwegian address (mock implementation)"""

        # Mock validation - in real implementation would use Posten's address API
        country = getattr(address, 'country', '').upper()
        postal_code = getattr(address, 'postal_code', '')

        # Basic Norwegian postal code validation
        if country == 'NO':
            if not postal_code or len(postal_code) != 4 or not postal_code.isdigit():
                return {
                    'valid': False,
                    'errors': ['Norwegian postal codes must be 4 digits'],
                    'suggestions': []
                }

        return {
            'valid': True,
            'normalized_address': address,
            'suggestions': []
        }
