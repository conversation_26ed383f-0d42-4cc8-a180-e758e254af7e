from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Carrier(models.Model):
    """Shipping carrier configurations"""
    
    title = models.CharField(max_length=100, help_text="Carrier title/name")
    code = models.CharField(
        max_length=20, unique=True,
        help_text="Unique carrier code (e.g., 'posten_bring')"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Is this carrier available for use?"
    )
    api_endpoint = models.URLField(
        blank=True, null=True,
        help_text="API endpoint URL for carrier integration"
    )
    api_key = models.CharField(
        max_length=255, blank=True, null=True,
        help_text="API key for carrier authentication"
    )
    api_secret = models.CharField(
        max_length=255, blank=True, null=True,
        help_text="API secret for carrier authentication"
    )
    base_cost = models.DecimalField(
        max_digits=6, decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        default=Decimal('0.00'),
        help_text="Base handling cost in USD"
    )
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Priority for carrier selection (higher = preferred)"
    )
    supports_tracking = models.BooleanField(
        default=True,
        help_text="Does this carrier support package tracking?"
    )
    max_weight = models.DecimalField(
        max_digits=8, decimal_places=2,
        validators=[MinValueValidator(Decimal('1.00'))],
        default=Decimal('20000.00'),
        help_text="Maximum weight this carrier can handle (grams)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Shipping Carrier"
        verbose_name_plural = "Shipping Carriers"
        ordering = ['-priority', 'title']

    def __str__(self):
        return self.title

    def can_handle_weight(self, weight):
        """Check if carrier can handle the given weight"""
        return weight <= self.max_weight


class CarrierService(models.Model):
    """Specific services offered by carriers"""
    
    carrier = models.ForeignKey(
        Carrier, on_delete=models.CASCADE,
        related_name='services'
    )
    service_name = models.CharField(
        max_length=100,
        help_text="Service name (e.g., 'Standard Parcel')"
    )
    service_code = models.CharField(
        max_length=50,
        help_text="Service code for API calls"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Is this service available?"
    )
    estimated_days = models.PositiveIntegerField(
        default=3,
        help_text="Estimated delivery days"
    )
    max_days = models.PositiveIntegerField(
        default=7,
        help_text="Maximum delivery days"
    )
    cost_multiplier = models.DecimalField(
        max_digits=4, decimal_places=2,
        default=Decimal('1.00'),
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Cost multiplier for this service"
    )
    supports_insurance = models.BooleanField(
        default=False,
        help_text="Does this service support insurance?"
    )
    supports_signature = models.BooleanField(
        default=False,
        help_text="Does this service support signature confirmation?"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Carrier Service"
        verbose_name_plural = "Carrier Services"
        unique_together = ('carrier', 'service_code')
        ordering = ['carrier__title', 'estimated_days', 'service_name']

    def __str__(self):
        return f"{self.carrier.title} - {self.service_name}"

    def get_estimated_delivery_range(self):
        """Get delivery time range as string"""
        if self.estimated_days == self.max_days:
            return f"{self.estimated_days} days"
        return f"{self.estimated_days}-{self.max_days} days"
