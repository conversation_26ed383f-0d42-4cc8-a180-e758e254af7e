from django.core.management.base import BaseCommand
from decimal import Decimal
from apps.shipping.models import Box, Carrier, CarrierService, PackingRule


class Command(BaseCommand):
    help = 'Populate initial shipping data (boxes, carriers, rules)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing shipping data...')
            PackingRule.objects.all().delete()
            CarrierService.objects.all().delete()
            Carrier.objects.all().delete()
            Box.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))

        self.stdout.write('Creating shipping boxes...')
        self.create_boxes()
        
        self.stdout.write('Creating carriers...')
        self.create_carriers()
        
        self.stdout.write('Creating packing rules...')
        self.create_packing_rules()
        
        self.stdout.write(self.style.SUCCESS('Successfully populated shipping data!'))

    def create_boxes(self):
        """Create standard shipping boxes"""
        boxes = [
            # Mailers
            {
                'title': 'Small Padded Mailer',
                'internal_length': Decimal('23.0'),
                'internal_width': Decimal('16.0'),
                'internal_height': Decimal('2.0'),
                'max_weight': Decimal('500.0'),
                'cost': Decimal('1.50'),
                'is_mailer': True,
                'priority': 10
            },
            {
                'title': 'Medium Padded Mailer',
                'internal_length': Decimal('30.0'),
                'internal_width': Decimal('23.0'),
                'internal_height': Decimal('3.0'),
                'max_weight': Decimal('1000.0'),
                'cost': Decimal('2.00'),
                'is_mailer': True,
                'priority': 9
            },
            {
                'title': 'Large Padded Mailer',
                'internal_length': Decimal('35.0'),
                'internal_width': Decimal('25.0'),
                'internal_height': Decimal('4.0'),
                'max_weight': Decimal('1500.0'),
                'cost': Decimal('2.50'),
                'is_mailer': True,
                'priority': 8
            },
            
            # Small boxes
            {
                'title': 'Extra Small Box',
                'internal_length': Decimal('15.0'),
                'internal_width': Decimal('10.0'),
                'internal_height': Decimal('8.0'),
                'max_weight': Decimal('1000.0'),
                'cost': Decimal('2.00'),
                'is_mailer': False,
                'priority': 7
            },
            {
                'title': 'Small Box',
                'internal_length': Decimal('20.0'),
                'internal_width': Decimal('15.0'),
                'internal_height': Decimal('10.0'),
                'max_weight': Decimal('2000.0'),
                'cost': Decimal('2.50'),
                'is_mailer': False,
                'priority': 6
            },
            {
                'title': 'Medium Box',
                'internal_length': Decimal('30.0'),
                'internal_width': Decimal('20.0'),
                'internal_height': Decimal('15.0'),
                'max_weight': Decimal('5000.0'),
                'cost': Decimal('3.50'),
                'is_mailer': False,
                'priority': 5
            },
            {
                'title': 'Large Box',
                'internal_length': Decimal('40.0'),
                'internal_width': Decimal('30.0'),
                'internal_height': Decimal('20.0'),
                'max_weight': Decimal('10000.0'),
                'cost': Decimal('5.00'),
                'is_mailer': False,
                'priority': 4
            },
            {
                'title': 'Extra Large Box',
                'internal_length': Decimal('50.0'),
                'internal_width': Decimal('40.0'),
                'internal_height': Decimal('30.0'),
                'max_weight': Decimal('15000.0'),
                'cost': Decimal('7.50'),
                'is_mailer': False,
                'priority': 3
            },
            {
                'title': 'Jumbo Box',
                'internal_length': Decimal('60.0'),
                'internal_width': Decimal('50.0'),
                'internal_height': Decimal('40.0'),
                'max_weight': Decimal('20000.0'),
                'cost': Decimal('10.00'),
                'is_mailer': False,
                'priority': 2
            }
        ]

        for box_data in boxes:
            box, created = Box.objects.get_or_create(
                title=box_data['title'],
                defaults=box_data
            )
            if created:
                self.stdout.write(f'  Created box: {box.title}')
            else:
                self.stdout.write(f'  Box already exists: {box.title}')

    def create_carriers(self):
        """Create shipping carriers"""
        # Posten Bring (Norway)
        carrier, created = Carrier.objects.get_or_create(
            code='posten_bring',
            defaults={
                'title': 'Posten Bring',
                'is_active': True,
                'base_cost': Decimal('0.00'),
                'priority': 10,
                'supports_tracking': True,
                'max_weight': Decimal('20000.00')
            }
        )
        
        if created:
            self.stdout.write(f'  Created carrier: {carrier.title}')
            
            # Create services for Posten Bring
            services = [
                {
                    'service_name': 'Standard Parcel',
                    'service_code': 'standard',
                    'estimated_days': 3,
                    'max_days': 5,
                    'cost_multiplier': Decimal('1.00'),
                    'supports_insurance': True,
                    'supports_signature': False
                },
                {
                    'service_name': 'Express Parcel',
                    'service_code': 'express',
                    'estimated_days': 1,
                    'max_days': 2,
                    'cost_multiplier': Decimal('1.50'),
                    'supports_insurance': True,
                    'supports_signature': True
                }
            ]
            
            for service_data in services:
                service = CarrierService.objects.create(
                    carrier=carrier,
                    **service_data
                )
                self.stdout.write(f'    Created service: {service.service_name}')
        else:
            self.stdout.write(f'  Carrier already exists: {carrier.title}')

    def create_packing_rules(self):
        """Create packing rules"""
        rules = [
            {
                'title': 'Small Items Mailer Rule',
                'description': 'Small, lightweight items should use mailers',
                'priority': 100,
                'max_weight': Decimal('500.0'),
                'max_volume': Decimal('1000.0'),
                'force_mailer': True,
                'additional_cost': Decimal('0.00'),
                'cost_multiplier': Decimal('1.00')
            },
            {
                'title': 'Medium Items Mailer Rule',
                'description': 'Medium lightweight items can use large mailers',
                'priority': 90,
                'max_weight': Decimal('1000.0'),
                'max_volume': Decimal('2000.0'),
                'force_mailer': True,
                'additional_cost': Decimal('0.50'),
                'cost_multiplier': Decimal('1.00')
            },
            {
                'title': 'Heavy Items Box Rule',
                'description': 'Heavy items require sturdy boxes',
                'priority': 80,
                'min_weight': Decimal('5000.0'),
                'force_mailer': False,
                'additional_cost': Decimal('2.00'),
                'cost_multiplier': Decimal('1.20')
            },
            {
                'title': 'Bulk Items Separate Packaging',
                'description': 'Orders with many items should be packaged separately',
                'priority': 70,
                'min_items': 5,
                'force_separate_packaging': True,
                'additional_cost': Decimal('1.00'),
                'cost_multiplier': Decimal('1.10')
            }
        ]

        for rule_data in rules:
            rule, created = PackingRule.objects.get_or_create(
                title=rule_data['title'],
                defaults=rule_data
            )
            if created:
                self.stdout.write(f'  Created rule: {rule.title}')
            else:
                self.stdout.write(f'  Rule already exists: {rule.title}')
