from collections import defaultdict
from django.db.models import Prefetch, Q
from django.utils.timezone import now
from django.http import JsonResponse
from rest_framework.viewsets import ModelViewSet, ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Min, Max
from utils.permissions import IsAdminOrReadOnly
from .serializers import (CategorySerializer, ProductSerializer, ReviewSerializer, ProductListSerializer,
                          ProductDetailSerializer) # Added ProductDetailSerializer
from utils.pagination import DefaultPagination
from .models import (Category, Product, ProductVariant, ProductType, ProductTypeAttribute, Review,
                     Brand, BrandProductType, Attribute, AttributeValue, Discount, ProductVariantAttributeValue
                     )


class CategoryViewSet(ModelViewSet):
    queryset = Category.objects.all().is_active().order_by('title')
    serializer_class = CategorySerializer
    permission_classes = [IsAdminOrReadOnly]


class ProductViewSet(ModelViewSet):
    http_method_names = ['get']
    # queryset = Product.objects.all().is_active()
    serializer_class = ProductSerializer
    lookup_field = 'slug'
    # filter_backends = [SearchFilter, OrderingFilter]
    # permission_classes = [IsAdminOrReadOnly]
    pagination_class = DefaultPagination

    # search_fields = ['title', 'description']
    # ordering_fields = ['product_variant__price']

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return ProductDetailSerializer # Use ProductDetailSerializer for detail view
        if self.action in ['list', 'list_product_by_category_slug']:
            return ProductListSerializer
        return ProductSerializer # Default or for other actions if any

    @action(
        methods=['get'],
        detail=False,
        url_path=r"category/(?P<slug>[\w-]+)",
    )
    def list_product_by_category_slug(self, request, slug=None):
        # Get the category by slug
        category = get_object_or_404(Category, slug=slug)

        # If the category has children, include products from all children categories
        if category.get_children().exists():
            # Get the IDs of the category and all its children
            category_ids = category.get_descendants(include_self=True).values_list('id', flat=True)
            queryset = self.get_queryset().filter(category_id__in=category_ids)
        else:
            # If the category has no children, return products only from this category
            queryset = self.get_queryset().filter(category=category)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def get_queryset(self):
        # queryset = Product.objects.all().is_active()

        queryset = Product.objects.is_active().prefetch_related(
            Prefetch(
                'product_variant',
                queryset=ProductVariant.objects.filter(is_active=True).prefetch_related(
                    Prefetch(
                        'attribute_value',
                        queryset=AttributeValue.objects.select_related('attribute')
                    ),
                    'product_image',
                    Prefetch(
                        'price_label',
                        queryset=AttributeValue.objects.select_related('attribute')
                    ),
                )
            ),
            Prefetch(
                'attribute_value',
                queryset=AttributeValue.objects.select_related('attribute')
            ),
            Prefetch(
                'reviews',
                queryset=Review.objects.select_related('customer')
            ),
            Prefetch(
                'product_variant__discounts',
                queryset=Discount.objects.filter(is_active=True, start_date__lte=now(), end_date__gte=now()),
                to_attr='active_discounts'
            )
        ).select_related(
            'product_type', # Needed for ProductDetailSerializer -> get_option_selectors
            'brand',
            'category' # Assuming category might be added to ProductDetailSerializer
        ).prefetch_related(
            # Prefetch for ProductTypeAttribute to help get_option_selectors
            # This helps fetch attributes linked to the product_type where is_option_selector=True
            Prefetch(
                'product_type__product_type_attribute_pt', # Access through ProductType's related name for ProductTypeAttribute
                queryset=ProductTypeAttribute.objects.filter(is_option_selector=True).select_related('attribute'),
                to_attr='option_selector_attributes_for_type' # Custom attr to store these
            )
        ).order_by('title')

        # always use .get method when getting query params, it returns None if params doesn't exist.
        min_price = self.request.query_params.get('min_price', None)
        max_price = self.request.query_params.get('max_price', None)
        search_query = self.request.query_params.get('search', None)
        sort_by = self.request.query_params.get('sort_by', None)
        brand_slug = self.request.query_params.get('brand', None)
        condition = self.request.query_params.get('condition', None)

        # Search filter
        if search_query:
            queryset = queryset.filter(title__icontains=search_query)
            # queryset = queryset.filter(Q(title__icontains=search_query) | Q(description__icontains=search_query))
            """
            This method works only with PostgresSQL.
            """
            # search_vector = SearchVector('title', weight='A') + SearchVector('description', weight='B')
            # search_query = SearchQuery(search_query)
            # queryset = queryset.annotate(
            #     search=search_vector,
            #     rank=SearchRank(search_vector, search_query)
            # ).filter(search=search_query).order_by('-rank')

        # Price range filters
        if min_price is not None and max_price is not None:
            queryset = queryset.filter(product_variant__price__range=(min_price, max_price))

        # Brand filter
        if brand_slug:
            queryset = queryset.filter(brand__slug=brand_slug)

        # Filter by condition
        if condition:
            queryset = queryset.filter(product_variant__condition=condition)

        """
        When new parameters are added, do not forget to add them to this list.
        Otherwise they won't work as expected.
        """
        for key in self.request.query_params.keys():
            if key not in ['min_price', 'max_price', 'brand', 'search', 'sort_by', 'page', 'condition']:
                values = self.request.query_params.getlist(key)
                query_filter = Q(
                    attribute_value__attribute__title=key,
                    attribute_value__attribute_value__in=values
                ) | Q(
                    product_variant__attribute_value__attribute__title=key,
                    product_variant__attribute_value__attribute_value__in=values
                )
                queryset = queryset.filter(query_filter)
                print(f"Filtered queryset: {queryset.values()}")

        if sort_by == 'title_asc':
            queryset = queryset.order_by('title')
        elif sort_by == 'title_desc':
            queryset = queryset.order_by('-title')
        elif sort_by == 'price_asc':
            queryset = queryset.annotate(min_price=Min('product_variant__price')).order_by('min_price')
        elif sort_by == 'price_desc':
            queryset = queryset.annotate(max_price=Max('product_variant__price')).order_by('-max_price')

        return queryset.distinct()


class ProductTypeAttributeValueViewSet(ViewSet):
    # serializer_class = FilteringAttributeValueSerializer

    def list(self, request):
        product_type_id = self.request.GET.get('product_type_id', None)

        # Frontend validation might have been done, but we still perform backend validation
        if product_type_id is not None:
            try:
                product_type_id = int(product_type_id)  # Ensure it's an integer
                if product_type_id <= 0:
                    raise ValueError("Invalid product type ID")
            except (ValueError, TypeError):
                return JsonResponse({'error': 'Invalid product type ID'}, status=400)

        # Proceed with the query only if product_type_id is not None and is valid
        if product_type_id is not None:
            try:
                product_type = get_object_or_404(
                    ProductType.objects.prefetch_related('attribute__attribute_value'),
                    id=product_type_id
                )

                # Filter attribute values where 'for_filtering=True' using Django's ORM
                attribute_values = product_type.attribute.filter(
                    attribute_value__for_filtering=True
                ).values_list('title', 'attribute_value__attribute_value')

                # Organize attribute values under their respective attribute titles using defaultdict
                attribute_value_dict = defaultdict(list)
                for title, value in attribute_values:
                    attribute_value_dict[title].append(value)

                return JsonResponse(attribute_value_dict)  # Return JSON response
            except ProductType.DoesNotExist:
                return JsonResponse({'error': 'Product type not found'}, status=404)
        else:
            return JsonResponse({'error': 'Product type ID not provided or invalid'}, status=400)


class ProductFilterOptionsViewSet(ViewSet):
    def list(self, request):
        product_type_id = request.query_params.get('product_type_id')

        # Initialize the filter options with price_range and condition
        filter_options = {
            'price_range': {
                'min': None,
                'max': None,
            },
            'condition': ProductVariant.CONDITION_CHOICES,
            'brands': [],
            'attribute_filters': {}
        }

        if product_type_id:
            # Filter the ProductVariant instances based on the product_type_id
            product_variants = ProductVariant.objects.filter(
                product__product_type_id=product_type_id,
                is_active=True
            )

            # Aggregate min and max price values only for the filtered ProductVariants
            price_range = product_variants.aggregate(
                min_price=Min('price'),
                max_price=Max('price')
            )
            filter_options['price_range']['min'] = price_range['min_price']
            filter_options['price_range']['max'] = price_range['max_price']
            # Use the BrandProductType model to filter brands
            brand_ids = BrandProductType.objects.filter(product_type_id=product_type_id).values_list('brand_id',
                                                                                                     flat=True)
            brands = Brand.objects.filter(id__in=brand_ids).values('id', 'slug')
            filter_options['brands'] = list(brands)

            # Filter attributes based on the product type
            attribute_ids = ProductTypeAttribute.objects.filter(product_type_id=product_type_id).values_list(
                'attribute_id', flat=True)
            attributes = Attribute.objects.filter(id__in=attribute_ids)

            for attribute in attributes:
                attribute_values = AttributeValue.objects.filter(attribute=attribute).values_list('attribute_value',
                                                                                                  flat=True)
                filter_options['attribute_filters'][attribute.title] = list(attribute_values)
        else:
            # If no product_type_id is provided, calculate price range across all ProductVariants
            product_variants = ProductVariant.objects.filter(is_active=True)
            price_range = product_variants.aggregate(
                min_price=Min('price'),
                max_price=Max('price')
            )
            filter_options['price_range']['min'] = price_range['min_price']
            filter_options['price_range']['max'] = price_range['max_price']

            # If no product type is selected, return all brands
            filter_options['brands'] = list(Brand.objects.values('slug', 'title'))

        return Response(filter_options)


class ReviewViewSet(ModelViewSet):
    serializer_class = ReviewSerializer

    # This queryset is for getting the all reviews of the product specified in product_slug
    def get_queryset(self):
        product_slug = self.kwargs.get('product_slug')
        product = get_object_or_404(Product.objects.all(), slug=product_slug)
        return Review.objects.filter(product=product)

    def get_serializer_context(self):
        product_slug = self.kwargs.get('product_slug')
        product = get_object_or_404(Product.objects.all(), slug=product_slug)
        return {'product': product}
