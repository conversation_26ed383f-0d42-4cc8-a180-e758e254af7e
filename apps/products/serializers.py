from django.db.models import Avg
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from rest_framework.serializers import SerializerMethodField
from apps.products.models import (Product, ProductVariant, ProductVariantAttributeValue, ProductType,
                                  ProductTypeAttribute, AttributeValue, Category, ProductImage,
                                  Attribute, Review
                                  )
from apps.customers.serializers import SimpleCustomer


class CategorySerializer(ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'title', 'slug', 'level', 'parent']


class SimpleCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['slug', 'title']


class ProductImageSerializer(ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alternative_text', 'order']

        # def get_image(self, obj):
        #     # Generate URL using Cloudinary URL builder with desired options
        #     return CloudinaryURL(
        #         public_id=obj.image.public_id,
        #         format=obj.image.format,
        #         transformation={'width': 200, 'height': 150, 'crop': 'fit'},  # adjust options as needed
        #     )


# class ProductTypeSerializer(ModelSerializer):
#     class Meta:
#         model = ProductType
#         fields = '__all__'

# class BrandSerializer(ModelSerializer):
#     class Meta:
#         model = Brand
#         fields = ['id', 'slug']


class AttributeSerializer(ModelSerializer):
    class Meta:
        model = Attribute
        fields = ['id', 'title']


class AttributeValueSerializer(serializers.ModelSerializer):
    attribute = AttributeSerializer(read_only=True)  # Made read_only as it's for representation

    class Meta:
        model = AttributeValue
        fields = ['id', 'attribute', 'attribute_value', 'is_active']


# class FilteringAttributeValueSerializer(ModelSerializer):
#     attribute = AttributeSerializer(many=False)
#
#     class Meta:
#         model = AttributeValue
#         fields = ['id', 'attribute', 'attribute_value']


class ReviewSerializer(ModelSerializer):
    # average_rating = serializers.SerializerMethodField()
    customer = SimpleCustomer()

    class Meta:
        model = Review
        fields = ['id', 'customer', 'title', 'description', 'rating', 'posted_at']

    def create(self, validated_data):
        product = self.context['product']
        validated_data['product'] = product
        return Review.objects.create(**validated_data)

    # def get_average_rating(self, obj):
    #     product = obj.product
    #     # Calculate average rating for the current product
    #     return Review.objects.filter(product=product).aggregate(Avg('rating'))['rating__avg']


class ProductVariantSerializer(ModelSerializer):
    product_image = ProductImageSerializer(many=True)
    attribute_value = AttributeValueSerializer(many=True)
    price_label = SerializerMethodField()
    price_label_attr_title = SerializerMethodField()
    discounted_price = serializers.SerializerMethodField()

    class Meta:
        model = ProductVariant
        # exclude = ['product']
        fields = [
            'id',
            'order',
            'price',
            'discounted_price',
            'price_label_attr_title',
            'price_label',
            'sku',
            'stock_qty',
            'is_active',
            'product_image',
            'attribute_value'
        ]

    # def get_price_label(self, instance):
    #     if instance.price_label:
    #         return instance.price_label.attribute_value
    #     return ''

    def get_price_label(self, instance):
        return instance.price_label.attribute_value if instance.price_label else ''

    # def get_price_label_attr_title(self, instance):
    #     if instance.price_label:
    #         # Use F() to access the related attribute title
    #         return instance.price_label.attribute.title
    #     return ''

    # def get_price_label_attr_title(self, instance):
    #     return instance.price_label.attribute.title if instance.price_label else ''

    def get_price_label_attr_title(self, instance):
        return instance.price_label.attribute.title if instance.price_label else ''

    # Original implementation of get_discounted_price
    # def get_discounted_price(self, obj):
    #     return obj.get_discounted_price()

    # Optimized solution 1
    # def get_discounted_price(self, obj):
    #     active_discounts = getattr(obj, 'active_discounts', [])
    #     if active_discounts:
    #         discount = active_discounts[0]  # Highest discount (ordered in Prefetch)
    #         from decimal import Decimal
    #         discount_multiplier = Decimal('1') - Decimal(str(discount.discount_percentage)) / Decimal('100')
    #         return obj.price * discount_multiplier
    #     return 0.0

    # Optimized solution 2
    def get_discounted_price(self, obj):
        active_discounts = getattr(obj, 'active_discounts', [])  # Use prefetched data
        if active_discounts:
            discount = active_discounts[0]
            from decimal import Decimal
            discount_multiplier = Decimal('1') - Decimal(str(discount.discount_percentage)) / Decimal('100')
            return obj.price * discount_multiplier
        return 0.0

    # def to_representation(self, instance):
    #     data = super().to_representation(instance)
    #     av_data = data.pop('attribute_value')
    #     attr_values = {}
    #     for key in av_data:
    #         attr_values.update({key['attribute']['title']: key['attribute_value']})
    #     data.update({'specification': attr_values})
    #     return data


class SelectableAttributeValueSerializer(serializers.ModelSerializer):
    attribute = AttributeSerializer(many=False)

    class Meta:
        model = AttributeValue
        fields = ['id', 'attribute', 'attribute_value', 'selectable']


class PVSelectableAttributeValueSerializer(ModelSerializer):
    attribute_value = SelectableAttributeValueSerializer(many=False)

    # product_variant = SimpleProductVariantSerializer()

    class Meta:
        model = ProductVariantAttributeValue
        fields = ['id', 'is_active', 'attribute_value', 'product_variant']


class ProductDetailSerializer(ModelSerializer):  # Renamed for clarity, or use existing ProductSerializer
    product_variant = ProductVariantSerializer(many=True, read_only=True,
                                               source='product_variant.all')  # Ensure only active variants if needed by filtering in view
    reviews = ReviewSerializer(many=True, read_only=True)
    average_rating = serializers.FloatField(read_only=True)
    option_selectors = serializers.SerializerMethodField()
    category = SimpleCategorySerializer(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'title',
            'slug',
            'category',
            'brand',
            'product_type',
            'description',
            'is_digital',
            'is_active',
            'average_rating',
            'product_variant',
            'option_selectors',
            'reviews',
        ]

    def get_option_selectors(self, obj):
        # obj is the Product instance
        option_selectors_data = []
        product_type = obj.product_type

        if not product_type:
            return []

        # Use the prefetched 'option_selector_attributes_for_type' from the Product object
        # This was set up in the ProductViewSet's get_queryset method
        selector_product_type_attributes = getattr(product_type, 'option_selector_attributes_for_type', [])

        # If not prefetched (e.g. serializer used outside that view context), fallback or handle error
        if not selector_product_type_attributes and ProductTypeAttribute.objects.filter(product_type=product_type,
                                                                                        is_option_selector=True).exists():
            # Fallback query if prefetch didn't happen, though ideally it should.
            # Or, you might raise an error or log a warning if prefetch is expected.
            selector_product_type_attributes = ProductTypeAttribute.objects.filter(
                product_type=product_type,
                is_option_selector=True
            ).select_related('attribute')

        active_product_variants = obj.product_variant.filter(is_active=True).prefetch_related(
            'attribute_value__attribute'  # Prefetch for efficiency
        )

        for pta in selector_product_type_attributes:
            attribute = pta.attribute
            # Collect all AttributeValue instances for this attribute from all active variants
            # Ensure these AttributeValues are active themselves
            value_ids_for_this_attribute = set()
            for variant in active_product_variants:
                for av_instance in variant.attribute_value.filter(attribute=attribute, is_active=True):
                    value_ids_for_this_attribute.add(av_instance.id)

            if not value_ids_for_this_attribute:
                continue

            # Get the AttributeValue objects
            # We use .order_by('attribute_value') to ensure consistent ordering for the frontend
            attribute_values = AttributeValue.objects.filter(
                id__in=list(value_ids_for_this_attribute),
                is_active=True  # Double check, though filtered above
            ).order_by('attribute_value')

            option_selectors_data.append({
                'attribute_id': attribute.id,
                'attribute_title': attribute.title,
                'values': [
                    {'value_id': val.id, 'value_text': val.attribute_value}
                    for val in attribute_values
                ]
            })
        return option_selectors_data


# Keep the original ProductSerializer if it's used for listings or other purposes,
# or adapt it. For clarity, I've named the detailed one ProductDetailSerializer.
# If ProductSerializer is meant to be the detail serializer, then the changes above apply to it.

# For example, if ProductSerializer is for general purposes, it might look simpler:
class ProductSerializer(ModelSerializer):
    # This could be a simplified variant serializer for listings
    # product_variant = ProductListVariantSerializer(many=True, read_only=True) 
    average_rating = serializers.FloatField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'title', 'slug', 'brand', 'product_type', 'average_rating',
            # 'main_image' # Often a main image is added here for listings
        ]


# The following serializers might need adjustment or removal based on the changes:
# SelectableAttributeValueSerializer - likely not needed in its current form
# PVSelectableAttributeValueSerializer - likely not needed in its current form

# Ensure ProductTypeAttributeSerializer is defined if used by ProductTypeSerializer
class ProductTypeAttributeSerializer(ModelSerializer):
    attributes = AttributeSerializer(many=True, source='attribute.all')

    class Meta:
        model = ProductType
        # fields = ['id', 'title', 'attributes']
        fields = '__all__'


class SimpleAttributeValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttributeValue
        fields = ['id', 'attribute_value']


class SimpleProductVariantSerializer(ModelSerializer):
    product_image = ProductImageSerializer(many=True)
    price_label = SimpleAttributeValueSerializer()

    class Meta:
        model = ProductVariant
        fields = ['id', 'price_label', 'product_image', 'stock_qty', 'price']


class SimpleProductSerializer(ModelSerializer):
    # product_variant = SimpleProductVariantSerializer(many=True)

    class Meta:
        model = Product
        fields = ['id', 'title', 'slug']


# Serializers for Product Lists
class ProductListVariantSerializer(ModelSerializer):
    product_image = ProductImageSerializer(many=True)

    class Meta:
        model = ProductVariant
        fields = ['id', 'price', 'product_image']


class ProductListSerializer(ModelSerializer):
    product_variant = ProductListVariantSerializer(many=True)

    class Meta:
        model = Product
        fields = ['id', 'title', 'slug', 'average_rating', 'product_variant', 'product_type']


class WishlistProductVariantSerializer(ModelSerializer):
    product_image = ProductImageSerializer(many=True)

    # price_label = SimpleAttributeValueSerializer()

    class Meta:
        model = ProductVariant
        fields = [
            'id',
            'price',
            # 'price_label',
            'product_image',
        ]


class WishlistProductSerializer(ModelSerializer):
    # category = CategorySerializer()
    product_variant = WishlistProductVariantSerializer(many=True)

    # attribute_value = AttributeValueSerializer(many=True)
    # reviews = ReviewSerializer(many=True)
    # average_rating = serializers.SerializerMethodField()
    # average_rating = serializers.FloatField(read_only=True)
    # product_variant_attribute_values = PVSelectableAttributeValueSerializer(
    #     many=True,
    #     source='product_variant__product_attribute_value_pv'
    # )
    # selectable_attribute_values = serializers.SerializerMethodField()

    # product_type = ProductTypeSerializer()
    # attribute = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id',
            'title',
            'slug',
            # 'brand',
            # 'product_id',
            # 'description',
            # 'is_digital',
            'is_active',
            # 'condition',
            # 'product_type',
            # 'category',
            'average_rating',  # Whenever new review is added, average rating is calculated and updated
            # 'reviews',
            'product_variant',
            # 'attribute_value',
            # 'selectable_attribute_values',
            # 'attribute'
        ]
