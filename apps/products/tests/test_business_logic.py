# Test business logic, edge cases, and complex scenarios
import pytest
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from model_bakery import baker
from apps.products.models import (
    Category, Product, ProductVariant, Review, ProductImage,
    ProductVariantAttributeValue, Attribute, AttributeValue,
    ProductType, ProductTypeAttribute, Brand, BrandProductType
)
from apps.customers.models import Customer


@pytest.mark.django_db
class TestCategoryBusinessLogic:
    """Test Category model business logic and edge cases"""
    
    def test_category_hierarchy_depth(self):
        """Test creating deep category hierarchies"""
        # Create a 4-level deep hierarchy
        level1 = Category.objects.create(title="Electronics")
        level2 = Category.objects.create(title="Computers", parent=level1)
        level3 = Category.objects.create(title="Laptops", parent=level2)
        level4 = Category.objects.create(title="Gaming Laptops", parent=level3)
        
        assert level4.level == 3  # 0-indexed
        assert level4.slug == "electronics-computers-laptops-gaming-laptops"
    
    def test_category_slug_collision_different_parents(self):
        """Test that same slug is allowed under different parents"""
        parent1 = baker.make(Category, title="Electronics")
        parent2 = baker.make(Category, title="Books")
        
        # Should be able to create "accessories" under both parents
        cat1 = Category.objects.create(title="Accessories", parent=parent1)
        cat2 = Category.objects.create(title="Accessories", parent=parent2)
        
        assert cat1.slug != cat2.slug  # Should have different slugs due to parent prefix
    
    def test_category_move_to_different_parent(self):
        """Test moving category to different parent"""
        electronics = baker.make(Category, title="Electronics", slug="electronics")
        books = baker.make(Category, title="Books", slug="books")
        accessories = Category.objects.create(title="Accessories", parent=electronics)
        
        original_slug = accessories.slug
        
        # Move to different parent
        accessories.parent = books
        accessories.save()
        
        # Slug should be updated to reflect new parent
        assert accessories.slug != original_slug
        assert "books" in accessories.slug
    
    def test_category_deletion_with_products(self):
        """Test that category with products cannot be deleted due to PROTECT"""
        category = baker.make(Category)
        baker.make(Product, category=category)
        
        with pytest.raises(Exception):  # Should raise ProtectedError
            category.delete()
    
    def test_category_deactivation_vs_deletion(self):
        """Test category deactivation instead of deletion"""
        category = baker.make(Category, is_active=True)
        baker.make(Product, category=category)
        
        # Should be able to deactivate instead of delete
        category.is_active = False
        category.save()
        
        assert not category.is_active
        # Category should not appear in active queryset
        assert category not in Category.objects.is_active()


@pytest.mark.django_db
class TestProductBusinessLogic:
    """Test Product model business logic and edge cases"""
    
    def test_product_average_rating_calculation_precision(self):
        """Test average rating calculation with various scenarios"""
        product = baker.make(Product, average_rating=0.0)
        customer1 = baker.make(Customer)
        customer2 = baker.make(Customer)
        customer3 = baker.make(Customer)
        
        # Create reviews with ratings that don't divide evenly
        baker.make(Review, product=product, customer=customer1, rating=1)
        baker.make(Review, product=product, customer=customer2, rating=2)
        baker.make(Review, product=product, customer=customer3, rating=5)
        
        product.update_average_rating()
        
        # Should be 2.67 (rounded)
        expected_avg = (1 + 2 + 5) / 3
        assert abs(product.average_rating - expected_avg) < 0.01
    
    def test_product_with_no_active_variants(self):
        """Test product behavior when all variants are inactive"""
        product = baker.make(Product, is_active=True)
        variant1 = baker.make(ProductVariant, product=product, is_active=False)
        variant2 = baker.make(ProductVariant, product=product, is_active=False)
        
        # Product should still exist but have no active variants
        assert product.is_active
        active_variants = product.product_variant.filter(is_active=True)
        assert active_variants.count() == 0
    
    def test_product_slug_uniqueness_across_products(self):
        """Test that product slugs should be unique"""
        baker.make(Product, slug="gaming-laptop")
        
        # Creating another product with same slug should be allowed
        # (assuming no unique constraint on slug in Product model)
        product2 = baker.make(Product, slug="gaming-laptop")
        assert product2.slug == "gaming-laptop"
    
    def test_product_with_multiple_categories_via_variants(self):
        """Test complex product-category relationships"""
        category1 = baker.make(Category, title="Laptops")
        category2 = baker.make(Category, title="Gaming")
        
        # Product belongs to one category
        product = baker.make(Product, category=category1)
        
        # But could conceptually be related to multiple through attributes
        # This tests the data model flexibility
        assert product.category == category1
        assert product.category != category2


@pytest.mark.django_db
class TestProductVariantBusinessLogic:
    """Test ProductVariant model business logic and edge cases"""
    
    def test_product_variant_order_field_auto_assignment(self):
        """Test OrderField auto-assignment for product variants"""
        product = baker.make(Product)
        
        # Create variants without specifying order
        variant1 = ProductVariant.objects.create(
            product=product,
            price=Decimal('100.00'),
            sku='SKU-1',
            stock_qty=10,
            weight=Decimal('100.00')
        )
        
        variant2 = ProductVariant.objects.create(
            product=product,
            price=Decimal('200.00'),
            sku='SKU-2',
            stock_qty=5,
            weight=Decimal('150.00')
        )
        
        # Orders should be auto-assigned
        assert variant1.order == 0
        assert variant2.order == 1
    
    def test_product_variant_stock_management(self):
        """Test stock quantity edge cases"""
        product = baker.make(Product)
        
        # Test zero stock
        variant = baker.make(ProductVariant, product=product, stock_qty=0)
        assert variant.stock_qty == 0
        
        # Test large stock numbers
        variant.stock_qty = 999999
        variant.save()
        assert variant.stock_qty == 999999
    
    def test_product_variant_price_precision(self):
        """Test price field precision and decimal handling"""
        product = baker.make(Product)
        
        # Test precise decimal values
        variant = ProductVariant.objects.create(
            product=product,
            price=Decimal('99.99'),
            sku='PRECISE-SKU',
            stock_qty=10,
            weight=Decimal('100.00')
        )
        
        assert variant.price == Decimal('99.99')
        
        # Test that price maintains precision
        variant.price = Decimal('123.45')
        variant.save()
        variant.refresh_from_db()
        assert variant.price == Decimal('123.45')
    
    def test_product_variant_weight_validation_edge_cases(self):
        """Test weight validation edge cases"""
        product = baker.make(Product)
        
        # Test minimum valid weight (0.00)
        variant = ProductVariant(
            product=product,
            price=Decimal('99.99'),
            sku='LIGHT-SKU',
            stock_qty=10,
            weight=Decimal('0.00')
        )
        variant.full_clean()  # Should not raise
        
        # Test very large weight
        variant.weight = Decimal('99999999.99')
        variant.full_clean()  # Should not raise
    
    def test_product_variant_condition_choices_validation(self):
        """Test condition field validation"""
        product = baker.make(Product)
        
        # Test valid conditions
        for condition in ['New', 'Used']:
            variant = baker.make(ProductVariant, product=product, condition=condition)
            assert variant.condition == condition
    
    def test_product_variant_deletion_cascade(self):
        """Test cascade deletion when product is deleted"""
        product = baker.make(Product)
        variant = baker.make(ProductVariant, product=product)
        variant_id = variant.id
        
        # Delete product should cascade to variants
        product.delete()
        
        assert not ProductVariant.objects.filter(id=variant_id).exists()


@pytest.mark.django_db
class TestProductImageBusinessLogic:
    """Test ProductImage model business logic and edge cases"""
    
    def test_product_image_order_auto_assignment(self):
        """Test OrderField auto-assignment for product images"""
        variant = baker.make(ProductVariant)
        
        # Create images without specifying order
        image1 = ProductImage.objects.create(
            product_variant=variant,
            alternative_text="First image",
            image="test1.jpg"
        )
        
        image2 = ProductImage.objects.create(
            product_variant=variant,
            alternative_text="Second image",
            image="test2.jpg"
        )
        
        # Orders should be auto-assigned
        assert image1.order == 0
        assert image2.order == 1
    
    def test_product_image_reordering(self):
        """Test manual reordering of product images"""
        variant = baker.make(ProductVariant)
        
        image1 = baker.make(ProductImage, product_variant=variant, order=0)
        image2 = baker.make(ProductImage, product_variant=variant, order=1)
        
        # Swap orders
        image1.order = 1
        image2.order = 0
        
        # Should be able to save without validation errors
        image1.save()
        image2.save()
        
        image1.refresh_from_db()
        image2.refresh_from_db()
        
        assert image1.order == 1
        assert image2.order == 0
    
    def test_product_image_deletion_order_gaps(self):
        """Test that deleting images creates order gaps"""
        variant = baker.make(ProductVariant)
        
        image1 = baker.make(ProductImage, product_variant=variant, order=0)
        image2 = baker.make(ProductImage, product_variant=variant, order=1)
        image3 = baker.make(ProductImage, product_variant=variant, order=2)
        
        # Delete middle image
        image2.delete()
        
        # Should have gap in ordering
        remaining_images = ProductImage.objects.filter(product_variant=variant).order_by('order')
        orders = [img.order for img in remaining_images]
        assert orders == [0, 2]  # Gap at 1


@pytest.mark.django_db
class TestAttributeBusinessLogic:
    """Test Attribute and AttributeValue business logic"""
    
    def test_product_variant_attribute_value_complex_validation(self):
        """Test complex PVAV validation scenarios"""
        # Create attribute and values
        color_attr = baker.make(Attribute, title="Color")
        red_value = baker.make(AttributeValue, attribute=color_attr, attribute_value="Red")
        blue_value = baker.make(AttributeValue, attribute=color_attr, attribute_value="Blue")
        
        size_attr = baker.make(Attribute, title="Size")
        large_value = baker.make(AttributeValue, attribute=size_attr, attribute_value="Large")
        
        variant = baker.make(ProductVariant)
        
        # Should be able to add different attribute types
        pvav1 = ProductVariantAttributeValue.objects.create(
            attribute_value=red_value,
            product_variant=variant
        )
        
        pvav2 = ProductVariantAttributeValue.objects.create(
            attribute_value=large_value,
            product_variant=variant
        )
        
        assert pvav1.attribute_value.attribute.title == "Color"
        assert pvav2.attribute_value.attribute.title == "Size"
        
        # Should not be able to add another color
        with pytest.raises(ValidationError):
            pvav3 = ProductVariantAttributeValue(
                attribute_value=blue_value,
                product_variant=variant
            )
            pvav3.save()
    
    def test_attribute_value_filtering_flag(self):
        """Test for_filtering flag on AttributeValue"""
        attribute = baker.make(Attribute, title="Brand")
        
        # Create values with different filtering flags
        filterable_value = baker.make(
            AttributeValue, 
            attribute=attribute, 
            attribute_value="Apple",
            for_filtering=True
        )
        
        non_filterable_value = baker.make(
            AttributeValue,
            attribute=attribute,
            attribute_value="Internal Code XYZ",
            for_filtering=False
        )
        
        # Test filtering
        filterable_values = AttributeValue.objects.filter(for_filtering=True)
        assert filterable_value in filterable_values
        assert non_filterable_value not in filterable_values


@pytest.mark.django_db
class TestReviewBusinessLogic:
    """Test Review model business logic and edge cases"""
    
    def test_review_automatic_product_rating_update(self):
        """Test that reviews automatically update product rating"""
        product = baker.make(Product, average_rating=0.0)
        customer1 = baker.make(Customer)
        customer2 = baker.make(Customer)
        
        # First review
        Review.objects.create(
            title="Great!",
            description="Love it",
            rating=5,
            product=product,
            customer=customer1
        )
        
        product.refresh_from_db()
        assert product.average_rating == 5.0
        
        # Second review
        Review.objects.create(
            title="Good",
            description="Pretty good",
            rating=3,
            product=product,
            customer=customer2
        )
        
        product.refresh_from_db()
        assert product.average_rating == 4.0  # (5 + 3) / 2
    
    def test_review_deletion_updates_product_rating(self):
        """Test that deleting reviews updates product rating"""
        product = baker.make(Product, average_rating=0.0)
        customer1 = baker.make(Customer)
        customer2 = baker.make(Customer)
        
        review1 = baker.make(Review, product=product, customer=customer1, rating=5)
        review2 = baker.make(Review, product=product, customer=customer2, rating=1)
        
        product.update_average_rating()
        assert product.average_rating == 3.0
        
        # Delete one review
        review1.delete()
        product.update_average_rating()
        
        assert product.average_rating == 1.0
    
    def test_multiple_reviews_same_customer(self):
        """Test that same customer can leave multiple reviews"""
        product = baker.make(Product)
        customer = baker.make(Customer)
        
        # Should be able to create multiple reviews from same customer
        review1 = baker.make(Review, product=product, customer=customer, title="First review")
        review2 = baker.make(Review, product=product, customer=customer, title="Second review")
        
        assert review1.customer == review2.customer
        assert Review.objects.filter(product=product, customer=customer).count() == 2
