# AAA : Arrange, Act, Assert
# from rest_framework.test import APIClient
from apps.products.models import Category
from model_bakery import baker
from rest_framework import status
import pytest


@pytest.fixture
def create_category(api_client):
    def do_create_category(category):
        return api_client.post('/api/products/categories/', category)

    return do_create_category


@pytest.mark.django_db
class TestCreateCategory:
    # @pytest.mark.skip(reason="Not implemented yet")
    def test_if_user_is_anonymous_returns_401(self, api_client, create_category):
        # Arrange

        # Act
        response = create_category({'title': 'Hard Drives'})

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.django_db
class TestRetrieveCategory:
    def test_if_category_exists_returns_200(self, api_client):
        # Arrange
        category = baker.make(Category)
        # Act
        response = api_client.get(f'/api/products/categories/{category.id}/')
        # Assert 
        assert response.status_code == status.HTTP_200_OK
        assert response.data == {
            'id': category.id,
            'title': category.title,
            'slug': category.slug,
            'level': category.level,
            'parent': category.parent
        }
