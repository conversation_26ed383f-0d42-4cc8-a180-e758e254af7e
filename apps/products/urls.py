from rest_framework_nested import routers
from . import views

router = routers.DefaultRouter()
router.register('categories', views.CategoryViewSet)
router.register('product-types', views.ProductTypeAttributeValueViewSet, basename='product-types')
router.register('product-filter-options', views.ProductFilterOptionsViewSet, basename='product-filter-options')
router.register('', views.ProductViewSet, basename='products')

# Nested router for product reviews
products_router = routers.NestedDefaultRouter(router, '', lookup='product')
products_router.register('reviews', views.ReviewViewSet, basename='product-reviews')

urlpatterns = router.urls + products_router.urls
