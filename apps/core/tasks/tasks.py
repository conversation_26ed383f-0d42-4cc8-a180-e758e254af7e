from celery import shared_task
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


@shared_task
def delete_unverified_user(user_id):
    try:
        user = User.objects.get(id=user_id)
        # If password_set is False and account was created more than 15 minutes ago, delete it
        if not user.password_set and (timezone.now() - user.date_joined).total_seconds() > 900:
            user.delete()
            return f"User with id {user_id} deleted due to inactivity."
        return f"User with id {user_id} already set password or is too new."
    except User.DoesNotExist:
        return f"User with id {user_id} does not exist."
