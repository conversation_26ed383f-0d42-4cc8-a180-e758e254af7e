from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    # layout of the user form in the admin interface when viewing or editing a user
    fieldsets = [
        (None, {'fields': ['email', 'password']}),
        # (_('Personal info'), {'fields': ['first_name', 'last_name']}),
        (_('Permissions'), {'fields': ['is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions']}),
        (_('Important dates'), {'fields': ['last_login', 'date_joined']}),
    ]
    # layout of the user form in the admin interface when creating a new user
    add_fieldsets = [
        (None, {
            'classes': ['wide'],
            'fields': ['email', 'password1', 'password2'],
        }),
    ]
    list_display = ['id', 'email', 'phone_number', 'is_staff', 'is_active', 'password_set',
                     'is_phone_verified', 'is_email_verified', 'last_login', 'date_joined']
    search_fields = ['email']
    ordering = ['id', 'email', 'is_staff', 'is_active',
                'last_login', 'date_joined']
    readonly_fields = ['last_login', 'date_joined']
    # list_filter = ['is_staff', 'is_active', 'groups']
