from django_redis.cache import RedisCache
from upstash_redis import Redis


class UpstashRedisCache(RedisCache):
    def __init__(self, server, params):
        params['client_class'] = 'custom_cache.UpstashRedisClient'
        super().__init__(server, params)


class UpstashRedisClient:
    def __init__(self, server, params):
        self.redis = Redis(url=params['URL'], token=params['TOKEN'])

    def get(self, key, default=None):
        return self.redis.get(key) or default

    def set(self, key, value, timeout=None):
        return self.redis.set(key, value, ex=timeout)

    def delete(self, key):
        return self.redis.delete(key)
