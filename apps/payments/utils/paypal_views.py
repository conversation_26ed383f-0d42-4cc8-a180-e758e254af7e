import requests
from django.conf import settings

PAYPAL_BASE_URL = "https://api-m.sandbox.paypal.com"  # Use live URL for production

PAYPAL_CLIENT_ID='AdeQV-ibVuEMgyeqK3AAAktk90w-0z4nND7qedDvjGazJliuI3F91nTDQ69uN4mhqxh46AWq5adsfvtR'
PAYPAL_SECRET='EE09uvXIbNLlVbsuFFManWvKNFZLs-WqK2gypJwEAFz-1jQI5mmnFzPpzHJfTdebZJ1Y-PpuC8LC9Txn'
# PAYPAL_ENVIRONMENT=sandbox


def get_paypal_access_token():
    auth = (PAYPAL_CLIENT_ID, PAYPAL_SECRET)
    headers = {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}
    response = requests.post(f"{PAYPAL_BASE_URL}/v1/oauth2/token", auth=auth, headers=headers, data={"grant_type": "client_credentials"})
    response.raise_for_status()
    return response.json()["access_token"]

def create_paypal_order(total_amount):
    access_token = get_paypal_access_token()
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
    payload = {
        "intent": "CAPTURE",
        "purchase_units": [{"amount": {"currency_code": "USD", "value": str(total_amount)}}],
    }
    response = requests.post(f"{PAYPAL_BASE_URL}/v2/checkout/orders", json=payload, headers=headers)
    response.raise_for_status()
    return response.json()

def capture_paypal_order(order_id):
    access_token = get_paypal_access_token()
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
    try:
        response = requests.post(f"{PAYPAL_BASE_URL}/v2/checkout/orders/{order_id}/capture", headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        # Log the full response for debugging
        print(f"PayPal Error Response: {e.response.text}")
        raise

