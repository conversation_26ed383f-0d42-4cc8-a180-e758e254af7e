# payments/braintree_config.py
# import braintree
# from django.conf import settings
#
# gateway = braintree.BraintreeGateway(
#     braintree.Configuration(
#         environment=braintree.Environment.Sandbox if settings.BRAINTREE_ENVIRONMENT == 'sandbox' else braintree.Environment.Production,
#         merchant_id=settings.BRAINTREE_MERCHANT_ID,
#         public_key=settings.BRAINTREE_PUBLIC_KEY,
#         private_key=settings.BRAINTREE_PRIVATE_KEY
#     )
# )
