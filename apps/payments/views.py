from django.http import JsonResponse
from django.db import transaction
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.exceptions import NotFound, ValidationError
from stripe import StripeError
from apps.order.models import Order
from .models import PaymentOption, PayPalOrder
from .serializers import PaymentOptionSerializer, CreatePayPalOrderSerializer
# from .utils.braintree_config import gateway
from .utils.paypal import PayPalClient
import stripe

stripe.api_key = settings.STRIPE_SECRET_KEY


class PaymentOptionViewSet(ReadOnlyModelViewSet):
    # permission_classes = [IsAdminOrReadOnly]
    queryset = PaymentOption.objects.filter(is_active=True).order_by('name')
    serializer_class = PaymentOptionSerializer


class StripeCheckoutView(APIView):
    def post(self, request):
        try:
            # Validate and get the amount
            amount = request.data.get('amount')
            email = request.data.get('email')
            name = request.data.get('name')
            order_id = request.data.get('order_id')
            # address = request.data.get('address')

            print("Amount: ", amount)

            if not all([
                amount,
                email,
                name,
                # address
                order_id
            ]):
                return Response({'error': 'Amount, email, name, and order_id are required.'},
                                status=status.HTTP_400_BAD_REQUEST)

            try:
                amount = int(amount)
                if amount <= 0:
                    raise ValueError
                # amount *= 100  # convert to cents
            except ValueError:
                return Response({'error': 'Amount must be a positive number.'}, status=status.HTTP_400_BAD_REQUEST)

            # Create a customer
            customer = stripe.Customer.create(
                email=email,
                name=name,
                # address=address
            )

            # Create a PaymentIntent with the order amount, currency, and customer
            intent = stripe.PaymentIntent.create(
                amount=amount,
                currency='usd',
                customer=customer.id,
                # payment_method_types=['card'],
                metadata={'order_id': order_id},
                automatic_payment_methods={'enabled': True},
            )

            return Response({
                'clientSecret': intent.client_secret
            })
        except stripe.error.StripeError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': 'An error occurred. Please try again later.'},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PaymentIntentClientSecretView(APIView):
    # PaymentIntent's client_secret is required to render the Payment Element in client-side.
    # Each PaymentIndent has its own client_secret.
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get `order_id` from query params
        order_id = request.query_params.get("order_id")

        if not order_id:
            return Response({"error": "order_id query parameter is required."}, status=400)

        # Retrieve the order associated with the authenticated user
        order = Order.objects.filter(id=order_id, customer__user=request.user).first()

        if not order or not order.payment_intent_id:
            raise NotFound("Order or Payment Intent not found.")

        # Retrieve the PaymentIntent client secret
        try:
            payment_intent = stripe.PaymentIntent.retrieve(order.payment_intent_id)
        except stripe.error.StripeError as e:
            return Response({"error": str(e)}, status=400)

        return Response({"client_secret": payment_intent.client_secret})


class ConfirmPaymentView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        # Get order_id from query parameters instead of path
        order_id = request.query_params.get('order_id')
        if not order_id:
            raise ValidationError("Order ID is required as a query parameter.")

        # Verify the order
        order = Order.objects.filter(id=order_id, customer__user=request.user).first()
        if not order:
            raise NotFound("Order not found.")

        # Retrieve the payment intent ID from the request body
        payment_intent_id = request.data.get("payment_intent_id")
        if not payment_intent_id:
            raise ValidationError("Payment Intent ID is required.")

        try:
            # Process payment with Stripe
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            if payment_intent.status == "succeeded":
                order.payment_status = "Paid"
                order.save()
                return Response({"message": "Payment confirmed and order updated."})
            else:
                return Response({"message": f"Payment not completed. Status: {payment_intent.status}"}, status=400)
        except stripe.error.StripeError as e:
            return Response({"error": str(e)}, status=400)


# Testing mode? Remember to follow Stripe instructions for testing.
# Step 1: Login to Stripe (stripe login)
# Step 2: Forward Event to this view. (stripe listen --forward-to localhost:8000/api/payments/stripe/webhook/)
# Step 3: Check if the event is a payment_intent.succeeded event.
class StripeWebhookView(APIView):
    """CSRF exempt as security is handled via Stripe signature verification."""

    @method_decorator(csrf_exempt)
    def post(self, request, *args, **kwargs):
        payload = request.body
        sig_header = request.META['HTTP_STRIPE_SIGNATURE']
        if not sig_header:
            return JsonResponse({'error': 'Missing Stripe signature header'}, status=400)
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET

        try:
            event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        except ValueError as e:
            return JsonResponse({'error': str(e)}, status=400)
        except stripe.error.SignatureVerificationError as e:
            return JsonResponse({'error': str(e)}, status=400)

        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            order_id = payment_intent['metadata']['order_id']
            try:
                order = Order.objects.get(id=order_id)
                order.payment_status = 'Paid'
                order.save()
            except Order.DoesNotExist:
                return JsonResponse({'error': 'Order not found'}, status=404)

            # Send email to user

        return JsonResponse({'status': 'success'}, status=200)


# PayPal Braintree views 
# class GenerateClientTokenView(APIView):
#     def get(self, request):
#         try:
#             client_token = gateway.client_token.generate()
#             return Response({'client_token': client_token})
#         except Exception as e:
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class ProcessPaymentView(APIView):
#     def post(self, request):
#         payment_method_nonce = request.data.get('payment_method_nonce')
#         order_id = request.data.get('order_id')
#
#         try:
#             order = Order.objects.get(id=order_id)
#             result = gateway.transaction.sale({
#                 'amount': str(order.total),
#                 'payment_method_nonce': payment_method_nonce,
#                 'order_id': str(order.id),
#                 'options': {
#                     'submit_for_settlement': True
#                 }
#             })
#
#             if result.is_success:
#                 # Create transaction record
#                 BraintreeTransaction.objects.create(
#                     order=order,
#                     transaction_id=result.transaction.id,
#                     amount=order.total,
#                     status='completed',
#                     payment_method=result.transaction.payment_instrument_type.lower()
#                 )
#
#                 # Update order status
#                 order.payment_status = 'Paid'
#                 order.save()
#
#                 return Response({
#                     'success': True,
#                     'transaction_id': result.transaction.id
#                 })
#             else:
#                 return Response({
#                     'success': False,
#                     'error': result.message
#                 }, status=status.HTTP_400_BAD_REQUEST)
#
#         except Order.DoesNotExist:
#             return Response(
#                 {'error': 'Order not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class BraintreeWebhookView(APIView):
#     def post(self, request):
#         webhook_notification = gateway.webhook_notification.parse(
#             request.POST["bt_signature"],
#             request.POST["bt_payload"]
#         )
#
#         if webhook_notification.kind == "transaction_settled":
#             transaction = webhook_notification.transaction
#             try:
#                 braintree_transaction = BraintreeTransaction.objects.get(
#                     transaction_id=transaction.id
#                 )
#                 braintree_transaction.status = 'settled'
#                 braintree_transaction.save()
#             except BraintreeTransaction.DoesNotExist:
#                 pass
#
#         return Response(status=status.HTTP_200_OK)


# PayPal views
class CreatePayPalOrderView(APIView):
    permission_classes = [IsAuthenticated]
    """
    Creates a PayPal order for checkout process
    Handles initial order creation in PayPal system
    This is a similar to creating a PaymentIntent in Stripe.
    """

    def post(self, request):
        serializer = CreatePayPalOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        order = Order.objects.get(id=serializer.validated_data['order_id'])

        if order.payment_status == "Paid":
            return Response(
                {"error": "Order has already been paid"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            paypal_client = PayPalClient()
            order_data = {
                "intent": "CAPTURE",
                "purchase_units": [
                    {
                        "reference_id": str(order.id),
                        "description": f"Order #{order.id}",
                        "amount": {
                            "currency_code": "USD",
                            "value": str(order.total)
                        }
                    }
                ]
            }

            paypal_response = paypal_client.create_order(order_data)

            # Update or create PayPal order
            PayPalOrder.objects.update_or_create(
                order=order,
                defaults={
                    'paypal_order_id': paypal_response["id"],
                    'status': paypal_response["status"]
                }
            )

            return Response({
                "id": paypal_response["id"],
                "status": paypal_response["status"]
            })

        except Exception as e:
            print("Unexpected Error:", str(e))
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CapturePayPalOrderView(APIView):
    permission_classes = [IsAuthenticated]
    """
    Handles PayPal payment capture and order status update
    Flow:
    1. Receives PayPal order ID after buyer approves payment
    2. Captures payment through PayPal API
    3. Updates both PayPal order and main order status
    4. Returns success response to trigger frontend redirect
    """

    def post(self, request):
        paypal_order_id = request.data.get("paypal_order_id")
        if not paypal_order_id:
            return Response(
                {"error": "PayPal order ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Initialize PayPal client and capture payment
            paypal_client = PayPalClient()
            capture_response = paypal_client.capture_order(paypal_order_id)

            with transaction.atomic():
                # Get our PayPal order record
                paypal_order = PayPalOrder.objects.select_related('order').get(paypal_order_id=paypal_order_id)

                # Update PayPal order status
                paypal_order.status = capture_response["status"]
                paypal_order.save()

                # Update main order payment status
                order = paypal_order.order
                order.payment_status = "Paid"
                order.save()

            return Response({
                "status": "COMPLETED",
                "message": "Payment captured and order updated successfully"
            })

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# At the moment, webhooks are not actively used for PayPal payment process
# For testing webhook in local environment, follow the steps here:
# https://dashboard.ngrok.com/get-started/setup/windows
class PayPalWebhookView(APIView):
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # print("1. Webhook received with data:", request.data)
            paypal_client = PayPalClient()

            verification_data = {
                "auth_algo": request.headers.get("PAYPAL-AUTH-ALGO"),
                "cert_url": request.headers.get("PAYPAL-CERT-URL"),
                "transmission_id": request.headers.get("PAYPAL-TRANSMISSION-ID"),
                "transmission_sig": request.headers.get("PAYPAL-TRANSMISSION-SIG"),
                "transmission_time": request.headers.get("PAYPAL-TRANSMISSION-TIME"),
                "webhook_id": settings.PAYPAL_WEBHOOK_ID,
                "webhook_event": request.data
            }

            # print("2. Verification data:", verification_data)
            verification_response = paypal_client.verify_webhook_signature(
                settings.PAYPAL_WEBHOOK_ID,
                verification_data
            )
            # print("3. Verification response:", verification_response)

            if verification_response["verification_status"] != "SUCCESS":
                return Response({"error": "Invalid webhook signature"}, status=status.HTTP_400_BAD_REQUEST)

            event_type = request.data["event_type"]
            resource = request.data["resource"]
            # print("4. Event type:", event_type)
            # print("5. Resource:", resource)

            if event_type == "PAYMENT.CAPTURE.COMPLETED":
                paypal_order_id = resource["links"][2]["href"].split("/")[-1]
                # print("6. PayPal order ID:", paypal_order_id)

                with transaction.atomic():
                    # Lock the row for update to prevent race conditions
                    paypal_order = PayPalOrder.objects.select_for_update().get(paypal_order_id=paypal_order_id)
                    order = paypal_order.order
                    # print("7. Found order:", order.id)

                    if order.payment_status != "Paid":
                        order.payment_status = "Paid"
                        order.save()
                        # print("8. Order updated successfully")

                return Response({"status": "Payment completed successfully"}, status=status.HTTP_200_OK)

            return Response({"status": "Webhook received"}, status=status.HTTP_200_OK)

        except Exception as e:
            # print("Error in webhook processing:", str(e))
            import traceback
            # print("Traceback:", traceback.format_exc())
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
