# Generated by Django 5.2.4 on 2025-07-24 13:19

import django.core.validators
import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('packing_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total packing cost for cart items', max_digits=6)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Shipping cost for cart', max_digits=6)),
                ('total_weight', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total weight in grams', max_digits=8)),
                ('total_volume', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Total volume in cubic centimeters', max_digits=12)),
                ('last_shipping_calculation', models.DateTimeField(blank=True, help_text='Last time shipping was calculated', null=True)),
            ],
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('extra_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Cart Items of Cart',
            },
        ),
    ]
