from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count, Sum, Q
from .models import CartProxy, CartItemProxy
from .serializers import (
    CartStaffSerializer, CartSummarySerializer, CartItemStaffSerializer,
    CartAnalyticsSerializer, BulkCartOperationSerializer, AbandonedCartSerializer
)
from .permissions import (
    CanManageCarts, CanViewCartAnalytics, CanManageAbandonedCarts
)
from .services import CartAnalyticsService, CartManagementService
from apps.staff.common.utils import paginate_queryset


class CartStaffViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for staff cart management (read-only with analytics)
    """
    queryset = CartProxy.objects.all().select_related('customer__user').prefetch_related(
        'cart_items__product', 'cart_items__product_variant'
    )
    permission_classes = [CanManageCarts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = [
        'customer__first_name', 'customer__last_name', 'customer__user__email'
    ]
    ordering_fields = ['created_at', 'customer__first_name']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CartSummarySerializer
        return CartStaffSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by customer if provided
        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        # Filter by abandoned status
        show_abandoned = self.request.query_params.get('abandoned')
        if show_abandoned == 'true':
            from django.utils import timezone
            from datetime import timedelta
            cutoff_date = timezone.now() - timedelta(hours=24)
            # This is a simplified filter - in production you'd want to optimize this
            abandoned_cart_ids = []
            for cart in queryset.prefetch_related('cart_items'):
                if cart.cart_items.exists():
                    last_activity = cart.get_last_activity()
                    if last_activity and last_activity < cutoff_date:
                        abandoned_cart_ids.append(cart.id)
            queryset = queryset.filter(id__in=abandoned_cart_ids)
        
        # Filter by has items
        has_items = self.request.query_params.get('has_items')
        if has_items == 'true':
            queryset = queryset.filter(cart_items__isnull=False).distinct()
        elif has_items == 'false':
            queryset = queryset.filter(cart_items__isnull=True)
        
        return queryset
    
    @action(detail=False, methods=['get'], permission_classes=[CanViewCartAnalytics])
    def analytics(self, request):
        """Get cart analytics data"""
        days = int(request.query_params.get('days', 30))
        analytics_data = CartAnalyticsService.get_cart_analytics(days)
        
        serializer = CartAnalyticsSerializer(analytics_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[CanManageAbandonedCarts])
    def abandoned(self, request):
        """Get abandoned carts"""
        days_threshold = int(request.query_params.get('days_threshold', 1))
        abandoned_carts = CartManagementService.get_abandoned_carts(days_threshold)
        
        # Paginate the results
        page = self.paginate_queryset(abandoned_carts)
        if page is not None:
            serializer = AbandonedCartSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = AbandonedCartSerializer(abandoned_carts, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_operations(self, request):
        """Perform bulk operations on carts"""
        serializer = BulkCartOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        cart_ids = serializer.validated_data['cart_ids']
        operation = serializer.validated_data['operation']
        reason = serializer.validated_data.get('reason', '')
        
        if operation == 'delete':
            result = CartManagementService.bulk_delete_carts(
                cart_ids=cart_ids,
                performed_by=request.user,
                reason=reason
            )
            
            return Response({
                'success': True,
                'message': f'Bulk delete completed',
                'deleted_count': result['deleted_count'],
                'errors': result['errors'],
                'total_requested': result['total_requested']
            })
        
        elif operation == 'clear':
            result = CartManagementService.bulk_clear_carts(
                cart_ids=cart_ids,
                performed_by=request.user,
                reason=reason
            )
            
            return Response({
                'success': True,
                'message': f'Bulk clear completed',
                'cleared_count': result['cleared_count'],
                'errors': result['errors'],
                'total_requested': result['total_requested']
            })
        
        elif operation == 'export':
            export_data = CartManagementService.export_cart_data(cart_ids)
            
            return Response({
                'success': True,
                'data': export_data,
                'count': len(export_data)
            })
        
        return Response({
            'success': False,
            'message': 'Invalid operation'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get detailed cart items for a specific cart"""
        cart = self.get_object()
        cart_items = cart.cart_items.select_related(
            'product', 'product_variant'
        ).all()
        
        serializer = CartItemStaffSerializer(cart_items, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get cart summary statistics"""
        queryset = self.get_queryset()
        
        total_carts = queryset.count()
        active_carts = queryset.filter(cart_items__isnull=False).distinct().count()
        anonymous_carts = queryset.filter(customer__isnull=True).count()
        registered_carts = queryset.filter(customer__isnull=False).count()
        
        # Calculate abandoned carts (simplified)
        abandoned_count = 0
        from django.utils import timezone
        from datetime import timedelta
        cutoff_date = timezone.now() - timedelta(hours=24)
        
        for cart in queryset.prefetch_related('cart_items')[:100]:  # Limit for performance
            if cart.cart_items.exists():
                last_activity = cart.get_last_activity()
                if last_activity and last_activity < cutoff_date:
                    abandoned_count += 1
        
        return Response({
            'total_carts': total_carts,
            'active_carts': active_carts,
            'anonymous_carts': anonymous_carts,
            'registered_carts': registered_carts,
            'abandoned_carts': abandoned_count,
            'abandonment_rate': (abandoned_count / active_carts * 100) if active_carts > 0 else 0
        })


class CartItemStaffViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for staff cart item management (read-only)
    """
    queryset = CartItemProxy.objects.all().select_related(
        'cart__customer__user', 'product', 'product_variant'
    )
    serializer_class = CartItemStaffSerializer
    permission_classes = [CanManageCarts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['product__title', 'cart__customer__user__email']
    ordering_fields = ['created_at', 'updated_at', 'quantity']
    ordering = ['-updated_at']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by cart if provided
        cart_id = self.request.query_params.get('cart_id')
        if cart_id:
            queryset = queryset.filter(cart_id=cart_id)
        
        # Filter by product if provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def popular_products(self, request):
        """Get most popular products in carts"""
        popular_items = self.get_queryset().values(
            'product__id', 'product__title'
        ).annotate(
            total_quantity=Sum('quantity'),
            cart_count=Count('cart', distinct=True)
        ).order_by('-total_quantity')[:20]
        
        return Response(list(popular_items))
