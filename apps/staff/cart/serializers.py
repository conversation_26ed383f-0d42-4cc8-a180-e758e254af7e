from rest_framework import serializers
from django.db.models import Sum, Count, Avg
from django.utils import timezone
from datetime import timedelta
from .models import CartProxy, CartItemProxy
from apps.customers.serializers import SimpleCustomerSerializer
from apps.products.serializers import SimpleProductSerializer, SimpleProductVariantSerializer


class CartItemStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff cart item management"""
    product = SimpleProductSerializer(read_only=True)
    product_variant = SimpleProductVariantSerializer(read_only=True)
    total_price = serializers.SerializerMethodField()

    class Meta:
        model = CartItemProxy
        fields = [
            'id', 'product', 'product_variant', 'quantity',
            'total_price', 'extra_data', 'created_at', 'updated_at'
        ]

    def get_total_price(self, obj):
        return obj.get_total_item_price()


class CartStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff cart management"""
    customer = SimpleCustomerSerializer(read_only=True)
    cart_items = CartItemStaffSerializer(many=True, read_only=True)
    total_value = serializers.SerializerMethodField()
    total_weight = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    is_abandoned = serializers.SerializerMethodField()
    last_activity = serializers.SerializerMethodField()

    class Meta:
        model = CartProxy
        fields = [
            'id', 'customer', 'created_at', 'cart_items',
            'total_value', 'total_weight', 'items_count',
            'is_abandoned', 'last_activity'
        ]

    def get_total_value(self, obj):
        return sum(item.get_total_item_price() for item in obj.cart_items.all())

    def get_total_weight(self, obj):
        return obj.get_cart_weight()

    def get_items_count(self, obj):
        return obj.cart_items.count()

    def get_is_abandoned(self, obj):
        return obj.is_abandoned()

    def get_last_activity(self, obj):
        return obj.get_last_activity()


class CartSummarySerializer(serializers.ModelSerializer):
    """Serializer for cart summary/list view"""
    customer_name = serializers.SerializerMethodField()
    customer_email = serializers.SerializerMethodField()
    total_value = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    is_abandoned = serializers.SerializerMethodField()
    last_activity = serializers.SerializerMethodField()

    class Meta:
        model = CartProxy
        fields = [
            'id', 'customer_name', 'customer_email', 'created_at',
            'total_value', 'items_count', 'is_abandoned', 'last_activity'
        ]

    def get_customer_name(self, obj):
        if obj.customer:
            return f"{obj.customer.first_name} {obj.customer.last_name}".strip()
        return "Anonymous"

    def get_customer_email(self, obj):
        return obj.customer.user.email if obj.customer else None

    def get_total_value(self, obj):
        return sum(item.get_total_item_price() for item in obj.cart_items.all())

    def get_items_count(self, obj):
        return obj.cart_items.count()

    def get_is_abandoned(self, obj):
        return obj.is_abandoned()

    def get_last_activity(self, obj):
        return obj.get_last_activity()


class CartAnalyticsSerializer(serializers.Serializer):
    """Serializer for cart analytics data"""
    total_carts = serializers.IntegerField()
    active_carts = serializers.IntegerField()
    abandoned_carts = serializers.IntegerField()
    anonymous_carts = serializers.IntegerField()
    registered_carts = serializers.IntegerField()
    average_cart_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_items_per_cart = serializers.FloatField()
    total_cart_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    abandonment_rate = serializers.FloatField()
    conversion_insights = serializers.DictField()


class BulkCartOperationSerializer(serializers.Serializer):
    """Serializer for bulk cart operations"""
    cart_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(choices=[
        ('delete', 'Delete Carts'),
        ('clear', 'Clear Cart Items'),
        ('export', 'Export Cart Data'),
    ])
    reason = serializers.CharField(max_length=500, required=False)


class AbandonedCartSerializer(serializers.ModelSerializer):
    """Serializer for abandoned cart management"""
    customer = SimpleCustomerSerializer(read_only=True)
    total_value = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    days_abandoned = serializers.SerializerMethodField()
    recovery_potential = serializers.SerializerMethodField()

    class Meta:
        model = CartProxy
        fields = [
            'id', 'customer', 'created_at', 'total_value',
            'items_count', 'days_abandoned', 'recovery_potential'
        ]

    def get_total_value(self, obj):
        return sum(item.get_total_item_price() for item in obj.cart_items.all())

    def get_items_count(self, obj):
        return obj.cart_items.count()

    def get_days_abandoned(self, obj):
        last_activity = obj.get_last_activity()
        if last_activity:
            return (timezone.now() - last_activity).days
        return 0

    def get_recovery_potential(self, obj):
        """Calculate recovery potential based on cart value and customer history"""
        total_value = self.get_total_value(obj)
        days_abandoned = self.get_days_abandoned(obj)

        # Simple scoring algorithm
        if total_value > 500:
            base_score = 'HIGH'
        elif total_value > 200:
            base_score = 'MEDIUM'
        else:
            base_score = 'LOW'

        # Reduce potential if abandoned too long
        if days_abandoned > 7:
            if base_score == 'HIGH':
                base_score = 'MEDIUM'
            elif base_score == 'MEDIUM':
                base_score = 'LOW'

        return base_score


class AbandonedCartSerializer(serializers.ModelSerializer):
    """Serializer for abandoned cart management"""
    customer = SimpleCustomerSerializer(read_only=True)
    total_value = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    days_abandoned = serializers.SerializerMethodField()
    recovery_potential = serializers.SerializerMethodField()
    
    class Meta:
        model = CartProxy
        fields = [
            'id', 'customer', 'created_at', 'total_value',
            'items_count', 'days_abandoned', 'recovery_potential'
        ]
    
    def get_total_value(self, obj):
        return sum(item.get_total_item_price() for item in obj.cart_items.all())
    
    def get_items_count(self, obj):
        return obj.cart_items.count()
    
    def get_days_abandoned(self, obj):
        last_activity = obj.get_last_activity()
        if last_activity:
            return (timezone.now() - last_activity).days
        return 0
    
    def get_recovery_potential(self, obj):
        """Calculate recovery potential based on cart value and customer history"""
        total_value = self.get_total_value(obj)
        days_abandoned = self.get_days_abandoned(obj)
        
        # Simple scoring algorithm
        if total_value > 500:
            base_score = 'HIGH'
        elif total_value > 200:
            base_score = 'MEDIUM'
        else:
            base_score = 'LOW'
        
        # Reduce potential if abandoned too long
        if days_abandoned > 7:
            if base_score == 'HIGH':
                base_score = 'MEDIUM'
            elif base_score == 'MEDIUM':
                base_score = 'LOW'
        
        return base_score
