from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

# Authorization models
from .authorization.models import StaffProfile, GroupMembership, PermissionAudit, APIAccessLog

# Products models
from .products.models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy, AttributeValueProxy,
    ProductVariantProxy, ProductImageProxy, ReviewProxy, DiscountProxy, ProductTypeAttributeProxy,
    ProductAttributeValueProxy, ProductVariantAttributeValueProxy, BrandProductTypeProxy,
    ProductAudit, BulkProductOperation
)

# Orders models
from .orders.models import (
    OrderProxy, OrderStatusHistory, OrderAssignment, OrderNote,
    OrderDocument
)
from .orders.models import BulkOrderOperation as OrderBulkOperation

# Customers models
from .customers.models import CustomerProxy, AddressProxy

# Payments models
from .payments.models import PaymentOptionProxy, PayPalOrderProxy, PaymentTransactionAudit

# Cart models
from .cart.models import CartProxy, CartItemProxy

# Wishlist models
from .wishlist.models import WishlistProxy


# =============================================================================
# AUTHORIZATION DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(StaffProfile)
class StaffProfileAdmin(admin.ModelAdmin):
    list_display = [
        'employee_id', 'user_email', 'position_title', 'department',
        'manager', 'status', 'hire_date'
    ]
    list_filter = ['department', 'status', 'hire_date']
    search_fields = ['employee_id', 'user__email', 'position_title']
    readonly_fields = ['employee_id', 'created_at', 'updated_at']
    date_hierarchy = 'hire_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'employee_id', 'position_title', 'department')
        }),
        ('Organizational', {
            'fields': ('manager', 'hire_date', 'status')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'manager__user')

    @admin.display(description='User Email')
    def user_email(self, obj):
        return obj.user.email

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Filter manager choices to only show active staff profiles
        if 'manager' in form.base_fields:
            form.base_fields['manager'].queryset = StaffProfile.objects.filter(
                status='ACTIVE'
            ).exclude(id=obj.id if obj else None)
        return form


@admin.register(GroupMembership)
class GroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'assigned_by', 'assigned_at', 'is_active']
    list_filter = ['group', 'is_active', 'assigned_at']
    search_fields = ['user__email', 'group__name']
    readonly_fields = ['assigned_at']
    date_hierarchy = 'assigned_at'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'group', 'assigned_by'
        )


@admin.register(PermissionAudit)
class PermissionAuditAdmin(admin.ModelAdmin):
    list_display = [
        'action', 'performed_by', 'target_user', 'target_group',
        'ip_address', 'timestamp'
    ]
    list_filter = ['action', 'timestamp']
    search_fields = [
        'performed_by__email', 'target_user__email', 'target_group__name'
    ]
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'performed_by', 'target_user', 'target_group'
        )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete audit logs
        return request.user.is_superuser


@admin.register(APIAccessLog)
class APIAccessLogAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'endpoint', 'method', 'status_code',
        'response_time', 'timestamp'
    ]
    list_filter = ['method', 'status_code', 'timestamp']
    search_fields = ['user__email', 'endpoint', 'ip_address']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete access logs
        return request.user.is_superuser


# =============================================================================
# PRODUCTS DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(ProductProxy)
class ProductProxyAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'brand', 'category', 'product_type', 'is_active',
        'created_at', 'get_variants_count'
    ]
    list_filter = ['is_active', 'brand', 'category', 'product_type', 'created_at']
    search_fields = ['title', 'description', 'brand__name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'brand', 'category', 'product_type')
        }),
        ('Status & Visibility', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'brand', 'category', 'product_type'
        ).prefetch_related('variants')

    @admin.display(description='Variants Count')
    def get_variants_count(self, obj):
        return obj.variants.count()


@admin.register(CategoryProxy)
class CategoryProxyAdmin(admin.ModelAdmin):
    list_display = ['title', 'parent', 'is_active', 'get_products_count']
    list_filter = ['is_active', 'parent']
    search_fields = ['title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'parent', 'slug')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('parent')

    @admin.display(description='Products Count')
    def get_products_count(self, obj):
        return obj.products.count()


@admin.register(BrandProxy)
class BrandProxyAdmin(admin.ModelAdmin):
    list_display = ['title', 'is_active', 'get_products_count']
    list_filter = ['is_active']
    search_fields = ['title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

    @admin.display(description='Products Count')
    def get_products_count(self, obj):
        return obj.products.count()


@admin.register(ProductTypeProxy)
class ProductTypeProxyAdmin(admin.ModelAdmin):
    list_display = ['title', 'parent', 'get_products_count', 'get_attributes_count']
    list_filter = ['parent']
    search_fields = ['title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'parent')
        }),
    )

    @admin.display(description='Products Count')
    def get_products_count(self, obj):
        return obj.products.count()

    @admin.display(description='Attributes Count')
    def get_attributes_count(self, obj):
        return obj.attributes.count()


@admin.register(AttributeProxy)
class AttributeProxyAdmin(admin.ModelAdmin):
    list_display = ['title', 'get_values_count']
    search_fields = ['title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title',)
        }),
    )

    @admin.display(description='Values Count')
    def get_values_count(self, obj):
        return obj.values.count()


@admin.register(AttributeValueProxy)
class AttributeValueProxyAdmin(admin.ModelAdmin):
    list_display = ['attribute_value', 'attribute', 'is_active']
    list_filter = ['attribute', 'is_active']
    search_fields = ['attribute_value', 'attribute__title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('attribute', 'attribute_value')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('attribute')


@admin.register(ProductVariantProxy)
class ProductVariantProxyAdmin(admin.ModelAdmin):
    list_display = [
        'product', 'sku', 'price', 'stock_qty', 'is_active',
        'get_attributes_display'
    ]
    list_filter = ['is_active', 'product__brand']
    search_fields = ['sku', 'product__title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('product', 'sku', 'price', 'stock_qty', 'weight', 'condition')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product')

    @admin.display(description='Attributes')
    def get_attributes_display(self, obj):
        attrs = obj.attribute_values.select_related('attribute', 'attribute_value')[:3]
        if attrs:
            display = ', '.join([f"{av.attribute.name}: {av.attribute_value.value}" for av in attrs])
            if obj.attribute_values.count() > 3:
                display += '...'
            return display
        return 'No attributes'


@admin.register(ProductImageProxy)
class ProductImageProxyAdmin(admin.ModelAdmin):
    list_display = ['get_product', 'image', 'alternative_text']
    search_fields = ['product_variant__product__title', 'alternative_text']

    def get_product(self, obj):
        return obj.product_variant.product.title
    get_product.short_description = 'Product'  # Column header in admin

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product_variant__product')


@admin.register(ReviewProxy)
class ReviewProxyAdmin(admin.ModelAdmin):
    list_display = [
        'product', 'customer', 'rating', 'title', 'posted_at'
    ]
    list_filter = ['rating', 'posted_at']
    search_fields = ['product__title', 'customer__user__email', 'title', 'description']
    readonly_fields = ['posted_at']

    fieldsets = (
        ('Review Information', {
            'fields': ('product', 'customer', 'rating', 'title', 'description')
        }),
        ('Timestamps', {
            'fields': ('posted_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product', 'customer__user')


@admin.register(DiscountProxy)
class DiscountProxyAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'discount_percentage', 'start_date', 'end_date', 'is_active'
    ]
    list_filter = ['is_active', 'start_date', 'end_date']
    search_fields = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'discount_percentage')
        }),
        ('Validity Period', {
            'fields': ('start_date', 'end_date')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )


@admin.register(ProductTypeAttributeProxy)
class ProductTypeAttributeProxyAdmin(admin.ModelAdmin):
    list_display = [
        'product_type', 'attribute', 'is_filterable', 'is_option_selector'
    ]
    list_filter = ['is_filterable', 'is_option_selector', 'product_type', 'attribute']
    search_fields = ['product_type__title', 'attribute__title']

    fieldsets = (
        ('Association', {
            'fields': ('product_type', 'attribute')
        }),
        ('Configuration', {
            'fields': ('is_filterable', 'is_option_selector')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product_type', 'attribute')


@admin.register(ProductAttributeValueProxy)
class ProductAttributeValueProxyAdmin(admin.ModelAdmin):
    list_display = ['product', 'attribute_value']
    search_fields = ['product__title', 'attribute_value__attribute__title', 'attribute_value__attribute_value']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product', 'attribute_value__attribute'
        )


@admin.register(ProductVariantAttributeValueProxy)
class ProductVariantAttributeValueProxyAdmin(admin.ModelAdmin):
    list_display = ['product_variant', 'attribute_value']
    search_fields = [
        'product_variant__sku', 'attribute_value__attribute__title', 'attribute_value__attribute_value'
    ]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product_variant__product', 'attribute_value__attribute'
        )


@admin.register(BrandProductTypeProxy)
class BrandProductTypeProxyAdmin(admin.ModelAdmin):
    list_display = ['brand', 'product_type']
    search_fields = ['brand__title', 'product_type__title']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('brand', 'product_type')


# =============================================================================
# ORDERS DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(OrderProxy)
class OrderProxyAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'customer_email', 'order_status', 'payment_status',
        'total', 'placed_at', 'get_items_count'
    ]
    list_filter = ['order_status', 'payment_status', 'placed_at']
    search_fields = ['id', 'customer__user__email', 'customer__first_name', 'customer__last_name']
    readonly_fields = ['placed_at']
    date_hierarchy = 'placed_at'

    fieldsets = (
        ('Order Information', {
            'fields': ('customer', 'order_status', 'payment_status', 'total')
        }),
        ('Shipping Information', {
            'fields': ('selected_address', 'payment_method'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('placed_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'customer__user', 'shipping_address', 'billing_address'
        ).prefetch_related('order_items')

    @admin.display(description='Customer Email')
    def customer_email(self, obj):
        return obj.customer.user.email if obj.customer else 'N/A'

    @admin.display(description='Items Count')
    def get_items_count(self, obj):
        return obj.order_items.count()


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'previous_status', 'new_status', 'changed_by', 'changed_at'
    ]
    list_filter = ['previous_status', 'new_status', 'changed_at']
    search_fields = ['order__id', 'changed_by__user__email']
    readonly_fields = ['changed_at']
    date_hierarchy = 'changed_at'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'order', 'changed_by__user'
        )

    def has_add_permission(self, request):
        return False  # Status changes should be done through order management

    def has_change_permission(self, request, obj=None):
        return False  # History should not be modified

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete history


@admin.register(OrderAssignment)
class OrderAssignmentAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'assigned_to', 'assigned_by', 'assigned_at', 'is_active'
    ]
    list_filter = ['is_active', 'assigned_at', 'assigned_to__department']
    search_fields = [
        'order__id', 'assigned_to__user__email', 'assigned_by__user__email'
    ]
    readonly_fields = ['assigned_at']
    date_hierarchy = 'assigned_at'

    fieldsets = (
        ('Assignment Information', {
            'fields': ('order', 'assigned_to', 'assigned_by')
        }),
        ('Status & Notes', {
            'fields': ('is_active', 'notes')
        }),
        ('Timestamps', {
            'fields': ('assigned_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'order', 'assigned_to__user', 'assigned_by__user'
        )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Filter assigned_to choices to only show active staff profiles
        if 'assigned_to' in form.base_fields:
            form.base_fields['assigned_to'].queryset = StaffProfile.objects.filter(
                status='ACTIVE'
            )
        return form


@admin.register(OrderNote)
class OrderNoteAdmin(admin.ModelAdmin):
    list_display = ['order', 'created_by', 'is_internal', 'created_at', 'get_note_preview']
    list_filter = ['is_internal', 'created_at', 'created_by__department']
    search_fields = ['order__id', 'note', 'created_by__user__email']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Note Information', {
            'fields': ('order', 'note', 'is_internal')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'order', 'created_by__user'
        )

    @admin.display(description='Note Preview')
    def get_note_preview(self, obj):
        return obj.note[:50] + '...' if len(obj.note) > 50 else obj.note


@admin.register(OrderBulkOperation)
class OrderBulkOperationAdmin(admin.ModelAdmin):
    list_display = [
        'operation_id', 'operation_type', 'staff_user', 'status',
        'total_items', 'processed_items', 'started_at'
    ]
    list_filter = ['operation_type', 'status', 'started_at']
    search_fields = ['operation_id', 'staff_user__email']
    readonly_fields = ['operation_id', 'started_at', 'completed_at']
    date_hierarchy = 'started_at'

    fieldsets = (
        ('Operation Information', {
            'fields': ('operation_id', 'operation_type', 'staff_user', 'status')
        }),
        ('Progress', {
            'fields': ('total_items', 'processed_items', 'failed_items')
        }),
        ('Data', {
            'fields': ('operation_data', 'results', 'error_message'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('staff_user')

    def has_add_permission(self, request):
        return False  # Operations should be created via API

    def has_change_permission(self, request, obj=None):
        return False  # Operations should not be manually modified

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete operation logs


@admin.register(OrderDocument)
class OrderDocumentAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'document_type', 'generated_by', 'generated_at', 'get_file_link'
    ]
    list_filter = ['document_type', 'generated_at']
    search_fields = ['order__id', 'generated_by__user__email']
    readonly_fields = ['generated_at']
    date_hierarchy = 'generated_at'

    fieldsets = (
        ('Document Information', {
            'fields': ('order', 'document_type', 'file_path')
        }),
        ('Generation Info', {
            'fields': ('generated_by', 'bulk_operation', 'generated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'order', 'generated_by__user', 'bulk_operation'
        )

    @admin.display(description='File')
    def get_file_link(self, obj):
        if obj.file_path:
            return format_html('<a href="{}" target="_blank">Download</a>', obj.file_path)
        return 'No file'


# =============================================================================
# CUSTOMERS DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(CustomerProxy)
class CustomerProxyAdmin(admin.ModelAdmin):
    list_display = [
        'get_full_name', 'user_email', 'phone_number', 'birth_date',
        'get_orders_count', 'get_total_spent'
    ]
    list_filter = ['birth_date', 'user__date_joined']
    search_fields = ['first_name', 'last_name', 'user__email', 'user__phone_number']
    readonly_fields = ['birth_date_change_count', 'user']

    fieldsets = (
        ('Personal Information', {
            'fields': ('user', 'first_name', 'last_name', 'birth_date')
        }),
        ('Metadata', {
            'fields': ('birth_date_change_count',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user').prefetch_related('orders')

    @admin.display(description='Full Name')
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()

    @admin.display(description='Email')
    def user_email(self, obj):
        return obj.user.email if obj.user else 'N/A'

    @admin.display(description='Phone')
    def phone_number(self, obj):
        return obj.user.phone_number if obj.user else 'N/A'

    @admin.display(description='Orders Count')
    def get_orders_count(self, obj):
        return obj.orders.count()

    @admin.display(description='Total Spent')
    def get_total_spent(self, obj):
        total = sum(order.total for order in obj.orders.all())
        return f"${total:.2f}"


@admin.register(AddressProxy)
class AddressProxyAdmin(admin.ModelAdmin):
    list_display = [
        'customer', 'full_name', 'city_or_village', 'postal_code',
        'get_orders_count'
    ]
    list_filter = ['city_or_village']
    search_fields = [
        'customer__first_name', 'customer__last_name', 'full_name',
        'city_or_village', 'postal_code'
    ]

    fieldsets = (
        ('Customer Information', {
            'fields': ('customer',)
        }),
        ('Address Details', {
            'fields': (
                'full_name', 'street_name', 'address_line_1', 'address_line_2',
                'city_or_village', 'postal_code'
            )
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('customer__user')

    @admin.display(description='Orders Count')
    def get_orders_count(self, obj):
        return obj.get_orders_count()


# =============================================================================
# PAYMENTS DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(PaymentOptionProxy)
class PaymentOptionProxyAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'slug', 'is_active', 'get_usage_count'
    ]
    list_filter = ['is_active']
    search_fields = ['name', 'slug']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )

    @admin.display(description='Usage Count')
    def get_usage_count(self, obj):
        return obj.get_usage_count()


@admin.register(PayPalOrderProxy)
class PayPalOrderProxyAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'paypal_order_id', 'status', 'get_amount', 'created_at'
    ]
    list_filter = ['status', 'created_at']
    search_fields = ['paypal_order_id', 'order__id']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Order Information', {
            'fields': ('order', 'paypal_order_id', 'status')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('order')

    @admin.display(description='Amount')
    def get_amount(self, obj):
        return f"${obj.order.total:.2f}" if obj.order else 'N/A'


@admin.register(PaymentTransactionAudit)
class PaymentTransactionAuditAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_id', 'payment_method', 'order_id', 'amount',
        'status', 'staff_user', 'created_at'
    ]
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['transaction_id', 'order_id', 'staff_user__email']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Information', {
            'fields': ('transaction_id', 'payment_method', 'order_id', 'customer_id', 'amount', 'status')
        }),
        ('Gateway Response', {
            'fields': ('gateway_response',),
            'classes': ('collapse',)
        }),
        ('Staff Information', {
            'fields': ('staff_user', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('staff_user')

    def has_add_permission(self, request):
        return False  # Audit logs should not be manually created

    def has_change_permission(self, request, obj=None):
        return False  # Audit logs should not be modified

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete audit logs


# =============================================================================
# CART DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(CartProxy)
class CartProxyAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'customer_name', 'get_items_count', 'get_total_value',
        'created_at', 'is_abandoned'
    ]
    list_filter = ['created_at']
    search_fields = ['customer__first_name', 'customer__last_name', 'customer__user__email']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Cart Information', {
            'fields': ('customer',)
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('customer__user').prefetch_related('cart_items')

    @admin.display(description='Customer')
    def customer_name(self, obj):
        if obj.customer:
            return f"{obj.customer.first_name} {obj.customer.last_name}".strip()
        return 'Anonymous'

    @admin.display(description='Items Count')
    def get_items_count(self, obj):
        return obj.cart_items.count()

    @admin.display(description='Total Value')
    def get_total_value(self, obj):
        total = sum(item.get_total_item_price() for item in obj.cart_items.all())
        return f"${total:.2f}"

    @admin.display(description='Abandoned', boolean=True)
    def is_abandoned(self, obj):
        return obj.is_abandoned()


@admin.register(CartItemProxy)
class CartItemProxyAdmin(admin.ModelAdmin):
    list_display = [
        'cart', 'product_variant', 'quantity', 'get_unit_price',
        'get_total_price', 'created_at'
    ]
    list_filter = ['created_at', 'product_variant__product__brand']
    search_fields = [
        'cart__customer__first_name', 'cart__customer__last_name',
        'product_variant__product__title', 'product_variant__sku'
    ]
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Item Information', {
            'fields': ('cart', 'product_variant', 'quantity')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'cart__customer', 'product_variant__product'
        )

    @admin.display(description='Unit Price')
    def get_unit_price(self, obj):
        return f"${obj.product_variant.price:.2f}"

    @admin.display(description='Total Price')
    def get_total_price(self, obj):
        return f"${obj.get_total_item_price():.2f}"


# =============================================================================
# WISHLIST DOMAIN ADMIN CLASSES
# =============================================================================

@admin.register(WishlistProxy)
class WishlistProxyAdmin(admin.ModelAdmin):
    list_display = [
        'customer', 'product', 'added_at'
    ]
    list_filter = ['added_at']
    search_fields = [
        'customer__first_name', 'customer__last_name', 'customer__user__email',
        'product__title'
    ]
    readonly_fields = ['added_at']
    date_hierarchy = 'added_at'

    fieldsets = (
        ('Wishlist Information', {
            'fields': ('customer', 'product')
        }),
        ('Timestamps', {
            'fields': ('added_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('customer__user', 'product')
