from rest_framework.permissions import BasePermission


class IsStaffUser(BasePermission):
    """
    Permission class to check if the user is staff
    Used for general staff API access
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_staff
        )


class IsSuperUser(BasePermission):
    """
    Permission class to check if the user is a superuser
    Used for system administration endpoints
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_superuser
        )


class CanManageGroups(BasePermission):
    """
    Permission class to check if the user can manage groups
    Requires specific group management permissions based on action
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated and is staff
        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        # Superusers can perform all operations
        if request.user.is_superuser:
            return True

        # Check specific permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            if action == 'create':
                return request.user.has_perm('auth.add_group')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('auth.change_group')
            elif action == 'destroy':
                return request.user.has_perm('auth.delete_group')
            elif action in ['list', 'retrieve']:
                return request.user.has_perm('auth.view_group')

        # Default to view permission for other actions
        return request.user.has_perm('auth.view_group')

    def has_object_permission(self, request, view, obj):
        # Check if the user is authenticated and is staff
        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        # Superusers can perform all operations
        if request.user.is_superuser:
            return True

        # Check specific permissions based on HTTP method
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return request.user.has_perm('auth.view_group')
        elif request.method in ['PUT', 'PATCH']:
            return request.user.has_perm('auth.change_group')
        elif request.method == 'DELETE':
            return request.user.has_perm('auth.delete_group')

        return False


class CanManageUsers(BasePermission):
    """
    Permission class to check if the user can manage other users
    Staff users can view and manage user-group assignments
    Staff status toggle requires specific permission
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # For staff status toggle, require specific permission
        if hasattr(view, 'action') and view.action == 'toggle_staff':
            return request.user.has_perm('core.can_toggle_staff_status')

        return True


class CanManageStaff(BasePermission):
    """
    Permission class for staff user management operations
    Requires specific staff management permissions
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always manage staff
        if request.user.is_superuser:
            return True

        # Check if user has the required permissions for staff management
        required_permissions = [
            'staff.change_staffprofile',
            'staff.view_staffprofile',
        ]

        return any(request.user.has_perm(perm) for perm in required_permissions)


class CanCreateStaff(BasePermission):
    """
    Permission class for creating new staff users
    Requires 'add_staffprofile' permission
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always create staff
        if request.user.is_superuser:
            return True

        # Check if user has the required permission
        return request.user.has_perm('staff.add_staffprofile')


class CanManageDepartment(BasePermission):
    """
    Permission class for department-level management
    Requires specific department management permissions
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can manage all departments
        if request.user.is_superuser:
            return True

        # Check if user has department management permissions
        department_permissions = [
            'staff.change_staffprofile',
            'auth.change_user',
            'auth.view_group',
        ]

        return any(request.user.has_perm(perm) for perm in department_permissions)


class CanViewAuditLogs(BasePermission):
    """
    Permission class to check if the user can view audit logs
    Only staff users with specific permissions can view audit logs
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always view audit logs
        if request.user.is_superuser:
            return True

        # Check if user has the specific audit viewing permission
        return request.user.has_perm('core.can_view_audit_logs')


class CanAccessStaffAPI(BasePermission):
    """
    Base permission for all staff API endpoints
    Ensures user is authenticated, active, and has staff status
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff
        )

    def has_object_permission(self, request, view, obj):
        """
        Object-level permission check
        Can be overridden by specific views for fine-grained control
        """
        return self.has_permission(request, view)
