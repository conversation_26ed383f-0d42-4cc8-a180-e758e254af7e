from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    GroupViewSet, UserViewSet, PermissionViewSet, AuditViewSet,
    RoleViewSet, StaffProfileViewSet, StaffUserCreateView
)
from .auth_views import CurrentUserView, UserPermissionsView, CheckPermissionView

router = DefaultRouter()
# router.register(r'groups', GroupViewSet, basename='auth-groups')
router.register(r'roles', RoleViewSet, basename='auth-roles')
router.register(r'users', UserViewSet, basename='auth-users')
router.register(r'staff-profiles', StaffProfileViewSet, basename='auth-staff-profiles')
router.register(r'staff-management', StaffUserCreateView, basename='auth-staff-management')
router.register(r'permissions', PermissionViewSet, basename='auth-permissions')
router.register(r'audit', AuditViewSet, basename='auth-audit')

app_name = 'authorization'

urlpatterns = [
    # Staff authorization endpoints (authentication handled by core app)
    # For login/logout, use: /api/auth/login/ and /api/auth/logout/
    path('auth/user/', CurrentUserView.as_view(), name='current-user'),
    path('auth/permissions/', UserPermissionsView.as_view(), name='user-permissions'),
    path('auth/check-permission/', CheckPermissionView.as_view(), name='check-permission'),

    # Include router URLs
    path('', include(router.urls)),
]
