from django.db.models.signals import pre_save
from django.dispatch import receiver
from ..models import StaffProfile


def generate_employee_id():
    """
    Generates an employee ID in the format EMP00001.
    Finds the max existing numeric suffix and increments it.
    """
    last_profile = StaffProfile.objects.order_by('-employee_id').first()
    if last_profile and last_profile.employee_id and last_profile.employee_id.startswith('EMP'):
        try:
            last_number = int(last_profile.employee_id[3:])
        except ValueError:
            last_number = 0
    else:
        last_number = 0

    next_number = last_number + 1
    return f"EMP{str(next_number).zfill(5)}"


@receiver(pre_save, sender=StaffProfile)
def set_employee_id(sender, instance, **kwargs):
    """
    Signal handler to auto-generate employee_id when creating a staff profile.
    Only sets employee_id if it's not already set.
    """
    if not instance.employee_id:
        instance.employee_id = generate_employee_id()
