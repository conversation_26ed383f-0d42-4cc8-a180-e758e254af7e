from django.contrib import admin
from .models import ProductAudit, BulkProductOperation


@admin.register(ProductAudit)
class ProductAuditAdmin(admin.ModelAdmin):
    """
    Minimal admin for product audit logs (debugging purposes)
    """
    list_display = ['id', 'product', 'staff_user', 'action', 'timestamp']
    list_filter = ['action', 'timestamp']
    search_fields = ['product__title', 'staff_user__email']
    readonly_fields = ['timestamp']
    ordering = ['-timestamp']
    
    def has_add_permission(self, request):
        return False  # Audit logs should not be manually created
    
    def has_change_permission(self, request, obj=None):
        return False  # Audit logs should not be modified
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete audit logs


@admin.register(BulkProductOperation)
class BulkProductOperationAdmin(admin.ModelAdmin):
    """
    Minimal admin for bulk operations (debugging purposes)
    """
    list_display = ['id', 'operation_type', 'staff_user', 'status', 'total_items', 'processed_items', 'started_at']
    list_filter = ['operation_type', 'status', 'started_at']
    search_fields = ['staff_user__email']
    readonly_fields = ['operation_id', 'started_at', 'completed_at']
    ordering = ['-started_at']
    
    def has_add_permission(self, request):
        return False  # Operations should be created via API
    
    def has_change_permission(self, request, obj=None):
        return False  # Operations should not be manually modified
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete operation logs
