from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.products.models import (
    Product, Category, Brand, ProductType, Attribute, AttributeValue,
    ProductVariant, ProductImage, Review, Discount, ProductTypeAttribute,
    ProductAttributeValue, ProductVariantAttributeValue, BrandProductType
)

User = get_user_model()


class ProductProxy(Product):
    """
    Proxy model for staff-specific product operations
    Adds staff-specific permissions and methods
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product"
        verbose_name_plural = "Staff Products"
        permissions = [
            ("bulk_update_products", "Can bulk update products"),
            ("change_product_status", "Can change product status"),
            ("manage_product_variants", "Can manage product variants"),
            ("manage_product_images", "Can manage product images"),
        ]

    def get_staff_summary(self):
        """Get summary information for the staff dashboard"""
        return {
            'id': self.id,
            'title': self.title,
            'sku_count': self.product_variant.count(),
            'active_variants': self.product_variant.filter(is_active=True).count(),
            'total_stock': sum(v.stock_qty for v in self.product_variant.all()),
            'average_rating': self.average_rating,
            'review_count': self.reviews.count(),
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
        }


class CategoryProxy(Category):
    """
    Proxy model for staff-specific category operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Category"
        verbose_name_plural = "Staff Categories"
        permissions = [
            ("move_category", "Can move category in tree"),
            ("bulk_update_categories", "Can bulk update categories"),
        ]


class BrandProxy(Brand):
    """
    Proxy model for staff-specific brand operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Brand"
        verbose_name_plural = "Staff Brands"
        permissions = [
            ("manage_brand_product_types", "Can manage brand product types"),
        ]


class ProductTypeProxy(ProductType):
    """
    Proxy model for staff-specific product type operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Type"
        verbose_name_plural = "Staff Product Types"
        permissions = [
            ("manage_type_attributes", "Can manage product type attributes"),
        ]


class AttributeProxy(Attribute):
    """
    Proxy model for staff-specific attribute operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Attribute"
        verbose_name_plural = "Staff Attributes"


class AttributeValueProxy(AttributeValue):
    """
    Proxy model for staff-specific attribute value operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Attribute Value"
        verbose_name_plural = "Staff Attribute Values"


class ProductVariantProxy(ProductVariant):
    """
    Proxy model for staff-specific product variant operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Variant"
        verbose_name_plural = "Staff Product Variants"
        permissions = [
            ("manage_variant_stock", "Can manage variant stock"),
            ("manage_variant_ordering", "Can manage variant ordering"),
        ]


class ProductImageProxy(ProductImage):
    """
    Proxy model for staff-specific product image operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Image"
        verbose_name_plural = "Staff Product Images"


class ReviewProxy(Review):
    """
    Proxy model for staff-specific review operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Review"
        verbose_name_plural = "Staff Reviews"
        permissions = [
            ("moderate_review", "Can moderate reviews"),
        ]


class DiscountProxy(Discount):
    """
    Proxy model for staff-specific discount operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Discount"
        verbose_name_plural = "Staff Discounts"
        permissions = [
            ("apply_discount", "Can apply discounts to variants"),
        ]


class BrandProductTypeProxy(BrandProductType):
    """
    Proxy model for staff-specific brand-product type operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Brand Product Type"
        verbose_name_plural = "Staff Brand Product Types"


class ProductTypeAttributeProxy(ProductTypeAttribute):
    """
    Proxy model for staff-specific product type attribute operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Type Attribute"
        verbose_name_plural = "Staff Product Type Attributes"


class ProductAttributeValueProxy(ProductAttributeValue):
    """
    Proxy model for staff-specific product attribute value operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Attribute Value"
        verbose_name_plural = "Staff Product Attribute Values"


class ProductVariantAttributeValueProxy(ProductVariantAttributeValue):
    """
    Proxy model for staff-specific product variant attribute value operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Product Variant Attribute Value"
        verbose_name_plural = "Staff Product Variant Attribute Values"


class ProductAudit(models.Model):
    """
    Track product changes by staff members
    """
    ACTION_CHOICES = [
        ('CREATE', 'Created'),
        ('UPDATE', 'Updated'),
        ('DELETE', 'Deleted'),
        ('BULK_UPDATE', 'Bulk Updated'),
        ('STATUS_CHANGE', 'Status Changed'),
        ('VARIANT_ADD', 'Variant Added'),
        ('VARIANT_UPDATE', 'Variant Updated'),
        ('VARIANT_DELETE', 'Variant Deleted'),
        ('IMAGE_ADD', 'Image Added'),
        ('IMAGE_UPDATE', 'Image Updated'),
        ('IMAGE_DELETE', 'Image Deleted'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='audit_logs'
    )
    staff_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='product_audit_logs'
    )
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    changes = models.JSONField(default=dict, help_text="JSON of field changes")
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['product', '-timestamp']),
            models.Index(fields=['staff_user', '-timestamp']),
            models.Index(fields=['action', '-timestamp']),
        ]

    def __str__(self):
        return f"{self.staff_user.email} {self.action} {self.product.title} at {self.timestamp}"


class BulkProductOperation(models.Model):
    """
    Track bulk operations on products
    """
    OPERATION_CHOICES = [
        ('BULK_CREATE', 'Bulk Create'),
        ('BULK_UPDATE', 'Bulk Update'),
        ('BULK_DELETE', 'Bulk Delete'),
        ('BULK_STATUS_CHANGE', 'Bulk Status Change'),
        ('BULK_CATEGORY_ASSIGN', 'Bulk Category Assignment'),
        ('BULK_ATTRIBUTE_ASSIGN', 'Bulk Attribute Assignment'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    operation_id = models.UUIDField(unique=True, editable=False)
    staff_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bulk_operations'
    )
    operation_type = models.CharField(max_length=30, choices=OPERATION_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    total_items = models.PositiveIntegerField()
    processed_items = models.PositiveIntegerField(default=0)
    failed_items = models.PositiveIntegerField(default=0)
    operation_data = models.JSONField(help_text="Operation parameters and data")
    results = models.JSONField(default=dict, help_text="Operation results and errors")
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['staff_user', '-started_at']),
            models.Index(fields=['status', '-started_at']),
            models.Index(fields=['operation_type', '-started_at']),
        ]

    def __str__(self):
        return f"{self.operation_type} by {self.staff_user.email} - {self.status}"

    @property
    def progress_percentage(self):
        """Calculate operation progress percentage"""
        if self.total_items == 0:
            return 0
        return (self.processed_items / self.total_items) * 100

    def mark_completed(self):
        """Mark the operation as completed"""
        self.status = 'COMPLETED'
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'completed_at'])

    def mark_failed(self, error_message):
        """Mark operation as failed"""
        self.status = 'FAILED'
        self.error_message = error_message
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'error_message', 'completed_at'])
