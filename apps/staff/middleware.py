from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
from .authorization.services import AuditService, SecurityService
import time


class StaffAPILoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all staff API access
    Only logs requests to /api/staff/ endpoints
    """
    
    def process_request(self, request):
        # Only process staff API requests
        if request.path.startswith('/api/staff/'):
            request._staff_start_time = time.time()
        return None
    
    def process_response(self, request, response):
        # Only log staff API requests
        if (request.path.startswith('/api/staff/') and 
            hasattr(request, '_staff_start_time') and 
            hasattr(request, 'user')):
            
            # Calculate response time
            response_time = time.time() - request._staff_start_time
            
            # Get client information
            security_service = SecurityService()
            ip_address = security_service.get_client_ip(request)
            user_agent = security_service.get_user_agent(request)
            
            # Log the API access
            AuditService.log_api_access(
                user=request.user if request.user.is_authenticated else None,
                endpoint=request.path,
                method=request.method,
                status_code=response.status_code,
                ip_address=ip_address,
                user_agent=user_agent,
                response_time=response_time
            )
        
        return response
    
    def process_exception(self, request, exception):
        """
        Log exceptions that occur during staff API requests
        """
        if request.path.startswith('/api/staff/'):
            # Log the exception
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Staff API Exception: {exception} for {request.method} {request.path}",
                exc_info=True
            )
        
        return None
