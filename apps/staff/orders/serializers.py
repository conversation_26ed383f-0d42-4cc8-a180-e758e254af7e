from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.order.models import Order, OrderItem
from apps.customers.serializers import SimpleCustomerSerializer, AddressSerializer
from apps.payments.serializers import PaymentMethodSerializer
from apps.products.serializers import SimpleProductSerializer, SimpleProductVariantSerializer
from apps.staff.authorization.serializers import StaffProfileSerializer
from .models import Order<PERSON>roxy, OrderStatusHistory, OrderAssignment, OrderNote, BulkOrderOperation, OrderDocument

User = get_user_model()


class StaffOrderItemSerializer(serializers.ModelSerializer):
    """Serializer for order items in staff context"""
    product = SimpleProductSerializer(read_only=True)
    product_variant = SimpleProductVariantSerializer(read_only=True)
    extra_data = serializers.JSONField()

    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_variant', 'extra_data', 'quantity', 'total_price']


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    """Serializer for order status history"""
    changed_by = StaffProfileSerializer(read_only=True)
    changed_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrderStatusHistory
        fields = ['id', 'previous_status', 'new_status', 'changed_by', 'changed_by_name',
                  'changed_at', 'notes']

    def get_changed_by_name(self, obj):
        if obj.changed_by:
            return f"{obj.changed_by.user.first_name} {obj.changed_by.user.last_name}".strip()
        return "System"


class OrderAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for order assignments"""
    assigned_to = StaffProfileSerializer(read_only=True)
    assigned_by = StaffProfileSerializer(read_only=True)
    assigned_to_name = serializers.SerializerMethodField()
    assigned_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrderAssignment
        fields = ['id', 'assigned_to', 'assigned_by', 'assigned_to_name', 'assigned_by_name',
                  'assigned_at', 'is_active', 'notes']

    def get_assigned_to_name(self, obj):
        return f"{obj.assigned_to.user.first_name} {obj.assigned_to.user.last_name}".strip()

    def get_assigned_by_name(self, obj):
        if obj.assigned_by:
            return f"{obj.assigned_by.user.first_name} {obj.assigned_by.user.last_name}".strip()
        return "System"


class OrderNoteSerializer(serializers.ModelSerializer):
    """Serializer for order notes"""
    created_by = StaffProfileSerializer(read_only=True)
    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrderNote
        fields = ['id', 'note', 'is_internal', 'created_by', 'created_by_name',
                  'created_at', 'updated_at']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.user.first_name} {obj.created_by.user.last_name}".strip()
        return "Unknown"


class StaffOrderSerializer(serializers.ModelSerializer):
    """
    Comprehensive serializer for orders in staff context.
    Includes additional fields and relationships for staff operations.
    """
    ordered_items = StaffOrderItemSerializer(many=True, read_only=True)
    customer = SimpleCustomerSerializer(read_only=True)
    selected_address = AddressSerializer(read_only=True)
    payment_method = PaymentMethodSerializer(read_only=True)
    shipping_cost = serializers.DecimalField(max_digits=6, decimal_places=2, read_only=True)

    # Staff-specific fields
    status_history = OrderStatusHistorySerializer(many=True, read_only=True)
    current_assignment = serializers.SerializerMethodField()
    staff_notes = OrderNoteSerializer(many=True, read_only=True)

    # Additional computed fields
    days_since_placed = serializers.SerializerMethodField()
    can_be_processed = serializers.SerializerMethodField()
    
    # Packing-specific computed fields for staff
    packing_summary = serializers.SerializerMethodField()
    warehouse_instructions = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'customer', 'placed_at', 'payment_status', 'order_status',
            'ordered_items', 'selected_address', 'payment_method', 'shipping_cost',
            'subtotal', 'total_weight', 'total', 'payment_intent_id',
            'packing_cost', 'total_volume', 'packing_details',
            # Staff-specific fields
            'status_history', 'current_assignment', 'staff_notes',
            'days_since_placed', 'can_be_processed', 'packing_summary', 'warehouse_instructions'
        ]

    def get_current_assignment(self, obj):
        """Get current active assignment"""
        assignment = obj.assignments.filter(is_active=True).first()
        if assignment:
            return OrderAssignmentSerializer(assignment).data
        return None

    def get_days_since_placed(self, obj):
        """Calculate days since order was placed"""
        from django.utils import timezone
        delta = timezone.now() - obj.placed_at
        return delta.days

    def get_can_be_processed(self, obj):
        """Check if current user can process this order"""
        request = self.context.get('request')
        if request and hasattr(request.user, 'staff_profile'):
            order_proxy = OrderProxy.objects.get(pk=obj.pk)
            return order_proxy.can_be_processed_by_staff(request.user.staff_profile)
        return False

    def get_packing_summary(self, obj):
        """Get a summary of packing information for quick reference"""
        packing_details = obj.packing_details or {}
        
        if not packing_details or packing_details.get('error'):
            return {
                'status': 'error',
                'message': 'Packing calculation failed - manual packing required',
                'fallback_used': packing_details.get('fallback_used', False)
            }
        
        boxes = packing_details.get('boxes', [])
        if not boxes:
            return {
                'status': 'no_packing',
                'message': 'No packing information available'
            }
        
        # Calculate summary statistics
        total_boxes = len(boxes)
        box_types = {}
        total_utilization = 0
        
        for box in boxes:
            box_title = box.get('box_title', 'Unknown Box')
            if box_title in box_types:
                box_types[box_title] += 1
            else:
                box_types[box_title] = 1
            total_utilization += box.get('utilization_percentage', 0)
        
        avg_utilization = round(total_utilization / total_boxes, 1) if total_boxes > 0 else 0
        
        return {
            'status': 'success',
            'total_boxes': total_boxes,
            'box_types': box_types,
            'average_utilization': f"{avg_utilization}%",
            'packing_method': packing_details.get('packing_method', 'unknown'),
            'calculation_time': f"{packing_details.get('calculation_time_seconds', 0)}s",
            'has_warnings': len(packing_details.get('warnings', [])) > 0,
            'unpacked_items': len(packing_details.get('unpacked_items', []))
        }

    def get_warehouse_instructions(self, obj):
        """Generate warehouse packing instructions"""
        packing_details = obj.packing_details or {}
        
        if not packing_details or packing_details.get('error'):
            return {
                'status': 'manual_packing_required',
                'instructions': [
                    'Packing calculation failed - use manual packing process',
                    f'Total weight: {obj.total_weight}g',
                    f'Estimated packing cost: ${obj.packing_cost}'
                ]
            }
        
        boxes = packing_details.get('boxes', [])
        if not boxes:
            return {
                'status': 'no_instructions',
                'instructions': ['No packing instructions available']
            }
        
        instructions = []
        
        # Add general instructions
        instructions.append(f"Pack order #{obj.id} using {len(boxes)} box(es)")
        
        if packing_details.get('warnings'):
            instructions.append("⚠️ WARNINGS:")
            for warning in packing_details.get('warnings', []):
                instructions.append(f"  - {warning}")
        
        # Add box-specific instructions
        for i, box in enumerate(boxes, 1):
            box_title = box.get('box_title', 'Unknown Box')
            dimensions = box.get('box_dimensions', {})
            utilization = box.get('utilization_percentage', 0)
            items = box.get('items', [])
            
            instructions.append(f"\nBox {i}: {box_title}")
            instructions.append(f"  Dimensions: {dimensions.get('length')}×{dimensions.get('width')}×{dimensions.get('height')}cm")
            instructions.append(f"  Utilization: {utilization}%")
            instructions.append(f"  Items to pack:")
            
            for item in items:
                instructions.append(f"    - {item.get('quantity')}× {item.get('product_title')} (SKU: {item.get('sku')})")
                instructions.append(f"      Weight: {item.get('weight')}g, Dimensions: {item.get('dimensions', {}).get('length')}×{item.get('dimensions', {}).get('width')}×{item.get('dimensions', {}).get('height')}cm")
        
        # Add unpacked items warning
        unpacked_items = packing_details.get('unpacked_items', [])
        if unpacked_items:
            instructions.append("\n❌ UNPACKED ITEMS (require manual handling):")
            for item in unpacked_items:
                instructions.append(f"  - SKU: {item.get('sku')} - Reason: {item.get('reason')}")
        
        return {
            'status': 'ready_to_pack',
            'instructions': instructions,
            'total_boxes': len(boxes),
            'estimated_time': f"{packing_details.get('calculation_time_seconds', 0)}s calculation time"
        }


class OrderStatusUpdateSerializer(serializers.Serializer):
    """Serializer for updating order status with validation"""
    order_status = serializers.ChoiceField(choices=Order.ORDER_STATUS)
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_order_status(self, value):
        """Validate status transition"""
        order = self.context.get('order')
        if not order:
            return value

        current_status = order.order_status

        # Define valid status transitions
        valid_transitions = {
            'Pending': ['Processing'],
            'Processing': ['Dispatched'],
            'Dispatched': ['Delivered'],
            'Delivered': []  # No transitions from delivered
        }

        if value not in valid_transitions.get(current_status, []):
            raise serializers.ValidationError(
                f"Cannot change status from '{current_status}' to '{value}'"
            )

        return value


class CreateOrderAssignmentSerializer(serializers.Serializer):
    """Serializer for creating order assignments"""
    assigned_to_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_assigned_to_id(self, value):
        """Validate that the assigned staff member exists and can handle orders"""
        from apps.staff.authorization.models import StaffProfile

        try:
            staff_profile = StaffProfile.objects.get(id=value)
        except StaffProfile.DoesNotExist:
            raise serializers.ValidationError("Staff member not found")

        # Check if staff member has order management permissions
        user_groups = set(staff_profile.user.groups.values_list('name', flat=True))
        from apps.staff.common.constants import STAFF_GROUPS

        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'],
            STAFF_GROUPS['ORDER_FULFILLMENT']
        }

        if not user_groups.intersection(allowed_groups):
            raise serializers.ValidationError(
                "Staff member does not have order management permissions"
            )

        return value


class CreateOrderNoteSerializer(serializers.Serializer):
    """Serializer for creating order notes"""
    note = serializers.CharField(max_length=1000)
    is_internal = serializers.BooleanField(default=True)


class WarehousePackingSerializer(serializers.ModelSerializer):
    """
    Specialized serializer for warehouse staff focusing on packing information
    """
    customer_name = serializers.SerializerMethodField()
    ordered_items = StaffOrderItemSerializer(many=True, read_only=True)
    packing_summary = serializers.SerializerMethodField()
    warehouse_instructions = serializers.SerializerMethodField()
    box_requirements = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'order_status', 'placed_at', 'customer_name',
            'ordered_items', 'total_weight', 'packing_cost', 'total_volume',
            'packing_details', 'packing_summary', 'warehouse_instructions', 'box_requirements'
        ]

    def get_customer_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}".strip()

    def get_packing_summary(self, obj):
        """Get packing summary - reuse from StaffOrderSerializer"""
        staff_serializer = StaffOrderSerializer()
        return staff_serializer.get_packing_summary(obj)

    def get_warehouse_instructions(self, obj):
        """Get warehouse instructions - reuse from StaffOrderSerializer"""
        staff_serializer = StaffOrderSerializer()
        return staff_serializer.get_warehouse_instructions(obj)

    def get_box_requirements(self, obj):
        """Get specific box requirements for warehouse inventory"""
        packing_details = obj.packing_details or {}
        boxes = packing_details.get('boxes', [])
        
        if not boxes:
            return []
        
        # Group boxes by type for inventory requirements
        box_requirements = {}
        for box in boxes:
            box_title = box.get('box_title', 'Unknown Box')
            box_id = box.get('box_id')
            dimensions = box.get('box_dimensions', {})
            
            if box_title in box_requirements:
                box_requirements[box_title]['quantity'] += 1
            else:
                box_requirements[box_title] = {
                    'box_id': box_id,
                    'quantity': 1,
                    'dimensions': dimensions,
                    'is_mailer': box.get('is_mailer', False)
                }
        
        return [
            {
                'box_title': title,
                'box_id': details['box_id'],
                'quantity_needed': details['quantity'],
                'dimensions': details['dimensions'],
                'is_mailer': details['is_mailer']
            }
            for title, details in box_requirements.items()
        ]


class OrderSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for order lists and summaries"""
    customer_name = serializers.SerializerMethodField()
    customer_email = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    days_since_placed = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'placed_at', 'payment_status', 'order_status',
            'total', 'customer_name', 'customer_email', 'items_count',
            'days_since_placed'
        ]

    def get_customer_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}".strip()

    def get_customer_email(self, obj):
        return obj.customer.user.email

    def get_items_count(self, obj):
        return obj.ordered_items.count()

    def get_days_since_placed(self, obj):
        from django.utils import timezone
        delta = timezone.now() - obj.placed_at
        return delta.days


# Bulk Operations Serializers

class BulkOrderStatusUpdateSerializer(serializers.Serializer):
    """Serializer for bulk order status updates"""
    order_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=100,
        help_text="List of order IDs to update (max 100)"
    )
    order_status = serializers.ChoiceField(choices=Order.ORDER_STATUS)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_order_ids(self, value):
        """Validate that all orders exist and can be updated"""
        existing_orders = Order.objects.filter(id__in=value)
        if existing_orders.count() != len(value):
            missing_ids = set(value) - set(existing_orders.values_list('id', flat=True))
            raise serializers.ValidationError(f"Orders not found: {missing_ids}")
        return value

    def validate(self, attrs):
        """Validate status transitions for all orders"""
        order_ids = attrs['order_ids']
        new_status = attrs['order_status']

        # Get current statuses
        orders = Order.objects.filter(id__in=order_ids).values('id', 'order_status')

        # Define valid transitions
        valid_transitions = {
            'Pending': ['Processing'],
            'Processing': ['Dispatched'],
            'Dispatched': ['Delivered'],
            'Delivered': []
        }

        invalid_orders = []
        for order in orders:
            current_status = order['order_status']
            if new_status not in valid_transitions.get(current_status, []):
                invalid_orders.append({
                    'order_id': order['id'],
                    'current_status': current_status,
                    'attempted_status': new_status
                })

        if invalid_orders:
            raise serializers.ValidationError({
                'invalid_transitions': invalid_orders,
                'message': 'Some orders cannot transition to the specified status'
            })

        return attrs


class BulkOrderAssignmentSerializer(serializers.Serializer):
    """Serializer for bulk order assignments"""
    order_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=50,
        help_text="List of order IDs to assign (max 50)"
    )
    assigned_to_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_assigned_to_id(self, value):
        """Validate that the assigned staff member exists and can handle orders"""
        from apps.staff.authorization.models import StaffProfile
        from apps.staff.common.constants import STAFF_GROUPS

        try:
            staff_profile = StaffProfile.objects.get(id=value)
        except StaffProfile.DoesNotExist:
            raise serializers.ValidationError("Staff member not found")

        # Check if staff member has order management permissions
        user_groups = set(staff_profile.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'],
            STAFF_GROUPS['ORDER_FULFILLMENT']
        }

        if not user_groups.intersection(allowed_groups):
            raise serializers.ValidationError(
                "Staff member does not have order management permissions"
            )

        return value


class BulkDocumentGenerationSerializer(serializers.Serializer):
    """Serializer for bulk document generation"""
    order_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=100,
        help_text="List of order IDs for document generation (max 100)"
    )
    document_types = serializers.ListField(
        child=serializers.ChoiceField(choices=OrderDocument.DOCUMENT_TYPES),
        min_length=1,
        help_text="Types of documents to generate"
    )
    include_customer_invoice = serializers.BooleanField(default=False)
    include_warranty_info = serializers.BooleanField(default=False)

    def validate_order_ids(self, value):
        """Validate that all orders exist"""
        existing_orders = Order.objects.filter(id__in=value)
        if existing_orders.count() != len(value):
            missing_ids = set(value) - set(existing_orders.values_list('id', flat=True))
            raise serializers.ValidationError(f"Orders not found: {missing_ids}")
        return value


class BulkOrderOperationSerializer(serializers.ModelSerializer):
    """Serializer for bulk operation tracking"""
    staff_user_email = serializers.CharField(source='staff_user.email', read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    duration = serializers.ReadOnlyField()
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = BulkOrderOperation
        fields = [
            'id', 'operation_id', 'staff_user', 'staff_user_email',
            'operation_type', 'operation_type_display', 'status', 'status_display',
            'total_items', 'processed_items', 'failed_items', 'progress_percentage',
            'started_at', 'completed_at', 'duration', 'error_message'
        ]
        read_only_fields = ['operation_id', 'started_at']


class OrderDocumentSerializer(serializers.ModelSerializer):
    """Serializer for order documents"""
    generated_by_name = serializers.SerializerMethodField()
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True)
    order_number = serializers.CharField(source='order.id', read_only=True)

    class Meta:
        model = OrderDocument
        fields = [
            'id', 'order', 'order_number', 'document_type', 'document_type_display',
            'generated_by', 'generated_by_name', 'bulk_operation',
            'generated_at', 'is_printed', 'printed_at', 'file_path'
        ]

    def get_generated_by_name(self, obj):
        if obj.generated_by:
            return f"{obj.generated_by.user.first_name} {obj.generated_by.user.last_name}".strip()
        return "System"
