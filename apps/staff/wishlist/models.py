from django.db import models
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from apps.wishlist.models import Wishlist


class WishlistProxy(Wishlist):
    """
    Proxy model for staff-specific wishlist operations
    Provides additional methods and behavior for staff wishlist management
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Wishlist"
        verbose_name_plural = "Staff Wishlists"
        permissions = [
            ("view_all_wishlists", "Can view all customer wishlists"),
            ("wishlist_analytics", "Can view wishlist analytics"),
            ("customer_behavior_analysis", "Can analyze customer behavior"),
            ("marketing_insights", "Can access marketing insights"),
            ("bulk_wishlist_operations", "Can perform bulk wishlist operations"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'customer': {
                'id': self.customer.id,
                'name': f"{self.customer.first_name} {self.customer.last_name}".strip(),
                'email': self.customer.user.email if self.customer.user else None,
            },
            'product': {
                'id': self.product.id,
                'title': self.product.title,
                'price': self.get_current_price(),
                'is_active': self.product.is_active,
                'category': self.product.category.title if self.product.category else None,
            },
            'added_at': self.added_at,
            'days_in_wishlist': self.get_days_in_wishlist(),
            'conversion_potential': self.get_conversion_potential(),
            'price_change': self.get_price_change_info(),
        }

    def get_current_price(self):
        """Get current price of the product"""
        if self.product.product_variant.exists():
            variant = self.product.product_variant.first()
            return float(variant.price)
        return 0.0

    def get_days_in_wishlist(self):
        """Get number of days item has been in wishlist"""
        return (timezone.now() - self.added_at).days

    def get_conversion_potential(self):
        """Calculate conversion potential based on various factors"""
        days_in_wishlist = self.get_days_in_wishlist()
        product_popularity = self.get_product_popularity()
        customer_activity = self.get_customer_activity_score()
        
        # Simple scoring algorithm
        base_score = 50  # Base 50% potential
        
        # Reduce potential if item has been in wishlist too long
        if days_in_wishlist > 30:
            base_score -= 20
        elif days_in_wishlist > 7:
            base_score -= 10
        
        # Increase potential for popular products
        if product_popularity > 0.7:
            base_score += 15
        elif product_popularity > 0.4:
            base_score += 5
        
        # Adjust for customer activity
        base_score += customer_activity * 10
        
        return max(0, min(100, base_score))

    def get_product_popularity(self):
        """Get product popularity score (0-1)"""
        # Count how many customers have this product in wishlist
        wishlist_count = WishlistProxy.objects.filter(product=self.product).count()
        
        # Simple popularity calculation (would be more sophisticated in real implementation)
        if wishlist_count > 50:
            return 1.0
        elif wishlist_count > 20:
            return 0.8
        elif wishlist_count > 10:
            return 0.6
        elif wishlist_count > 5:
            return 0.4
        else:
            return 0.2

    def get_customer_activity_score(self):
        """Get customer activity score (0-1)"""
        # Check customer's recent activity
        recent_orders = self.customer.order_set.filter(
            placed_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        recent_wishlist_additions = self.customer.wishlists.filter(
            added_at__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # Simple activity scoring
        activity_score = 0
        if recent_orders > 0:
            activity_score += 0.5
        if recent_wishlist_additions > 1:
            activity_score += 0.3
        if self.customer.user and self.customer.user.last_login:
            days_since_login = (timezone.now() - self.customer.user.last_login).days
            if days_since_login < 7:
                activity_score += 0.2
        
        return min(1.0, activity_score)

    def get_price_change_info(self):
        """Get price change information since added to wishlist"""
        # This would track historical prices
        # For now, return placeholder data
        return {
            'original_price': self.get_current_price(),
            'current_price': self.get_current_price(),
            'price_change': 0.0,
            'price_change_percent': 0.0,
            'is_on_sale': False,
        }

    def is_abandoned(self, days_threshold=30):
        """Check if wishlist item is abandoned"""
        return self.get_days_in_wishlist() > days_threshold

    def get_marketing_insights(self):
        """Get marketing insights for this wishlist item"""
        return {
            'recommended_action': self.get_recommended_marketing_action(),
            'target_discount': self.get_target_discount(),
            'urgency_level': self.get_urgency_level(),
            'customer_segment': self.customer.get_customer_segment() if hasattr(self.customer, 'get_customer_segment') else 'UNKNOWN',
        }

    def get_recommended_marketing_action(self):
        """Get recommended marketing action"""
        days_in_wishlist = self.get_days_in_wishlist()
        conversion_potential = self.get_conversion_potential()
        
        if days_in_wishlist > 30 and conversion_potential < 30:
            return 'ABANDON_RECOVERY'
        elif days_in_wishlist > 7 and conversion_potential > 60:
            return 'DISCOUNT_OFFER'
        elif conversion_potential > 80:
            return 'URGENCY_REMINDER'
        else:
            return 'GENTLE_REMINDER'

    def get_target_discount(self):
        """Get recommended discount percentage"""
        conversion_potential = self.get_conversion_potential()
        days_in_wishlist = self.get_days_in_wishlist()
        
        if days_in_wishlist > 30:
            return 20  # 20% discount for old items
        elif days_in_wishlist > 14:
            return 15  # 15% discount for medium-aged items
        elif conversion_potential > 80:
            return 5   # Small discount for high-potential items
        else:
            return 10  # Standard discount

    def get_urgency_level(self):
        """Get urgency level for marketing"""
        conversion_potential = self.get_conversion_potential()
        days_in_wishlist = self.get_days_in_wishlist()
        
        if days_in_wishlist > 30:
            return 'HIGH'  # Risk of abandonment
        elif conversion_potential > 80:
            return 'MEDIUM'  # Good conversion potential
        else:
            return 'LOW'
