from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count, Sum, Q
from .models import WishlistProxy
from .serializers import (
    WishlistStaffSerializer, WishlistSummarySerializer, WishlistAnalyticsSerializer,
    CustomerBehaviorSerializer, MarketingInsightsSerializer, BulkWishlistOperationSerializer,
    ConversionOpportunitySerializer
)
from .permissions import (
    CanManageWishlists, CanViewWishlistAnalytics, CanAccessCustomerBehavior,
    CanAccessMarketingInsights
)
from .services import WishlistAnalyticsService, CustomerBehaviorService, MarketingInsightsService
from apps.staff.common.utils import paginate_queryset


class WishlistStaffViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for staff wishlist management (read-only with analytics)
    """
    queryset = WishlistProxy.objects.all().select_related(
        'customer__user', 'product__category'
    ).prefetch_related('product__product_variant')
    permission_classes = [CanManageWishlists]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = [
        'customer__first_name', 'customer__last_name', 'customer__user__email',
        'product__title', 'product__category__title'
    ]
    ordering_fields = ['added_at', 'customer__first_name', 'product__title']
    ordering = ['-added_at']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return WishlistSummarySerializer
        return WishlistStaffSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by customer if provided
        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        # Filter by product if provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        
        # Filter by category if provided
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(product__category__title__icontains=category)
        
        # Filter by conversion potential
        min_potential = self.request.query_params.get('min_conversion_potential')
        if min_potential:
            # This would need to be implemented with a custom filter
            # For now, just return the queryset
            pass
        
        # Filter abandoned items
        show_abandoned = self.request.query_params.get('abandoned')
        if show_abandoned == 'true':
            # This would filter items older than 30 days
            from django.utils import timezone
            from datetime import timedelta
            cutoff_date = timezone.now() - timedelta(days=30)
            queryset = queryset.filter(added_at__lt=cutoff_date)
        
        return queryset
    
    @action(detail=False, methods=['get'], permission_classes=[CanViewWishlistAnalytics])
    def analytics(self, request):
        """Get wishlist analytics data"""
        days = int(request.query_params.get('days', 30))
        analytics_data = WishlistAnalyticsService.get_wishlist_analytics(days)
        
        serializer = WishlistAnalyticsSerializer(analytics_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[CanAccessCustomerBehavior])
    def behavior_analysis(self, request):
        """Get customer behavior analysis"""
        behavior_data = CustomerBehaviorService.get_customer_behavior_analysis()
        
        serializer = CustomerBehaviorSerializer(behavior_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[CanAccessMarketingInsights])
    def marketing_insights(self, request):
        """Get marketing insights and recommendations"""
        insights_data = MarketingInsightsService.get_marketing_insights()
        
        serializer = MarketingInsightsSerializer(insights_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], permission_classes=[CanAccessMarketingInsights])
    def conversion_opportunities(self, request):
        """Get high-potential conversion opportunities"""
        insights_data = MarketingInsightsService.get_marketing_insights()
        opportunities = insights_data['conversion_opportunities']
        
        # Paginate the results
        page = self.paginate_queryset(opportunities)
        if page is not None:
            serializer = ConversionOpportunitySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = ConversionOpportunitySerializer(opportunities, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_operations(self, request):
        """Perform bulk operations on wishlist items"""
        serializer = BulkWishlistOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        wishlist_ids = serializer.validated_data['wishlist_ids']
        operation = serializer.validated_data['operation']
        
        if operation == 'remove':
            # Remove items from wishlist
            deleted_count = WishlistProxy.objects.filter(
                id__in=wishlist_ids
            ).delete()[0]
            
            return Response({
                'success': True,
                'message': f'Removed {deleted_count} items from wishlists',
                'deleted_count': deleted_count
            })
        
        elif operation == 'export':
            # Export wishlist data
            wishlist_items = WishlistProxy.objects.filter(
                id__in=wishlist_ids
            ).select_related('customer__user', 'product')
            
            export_data = [
                {
                    'wishlist_id': item.id,
                    'customer_email': item.customer.user.email if item.customer.user else None,
                    'customer_name': f"{item.customer.first_name} {item.customer.last_name}".strip(),
                    'product_title': item.product.title,
                    'product_price': item.get_current_price(),
                    'added_at': item.added_at.isoformat(),
                    'days_in_wishlist': item.get_days_in_wishlist(),
                    'conversion_potential': item.get_conversion_potential(),
                }
                for item in wishlist_items
            ]
            
            return Response({
                'success': True,
                'data': export_data,
                'count': len(export_data)
            })
        
        elif operation == 'marketing_campaign':
            # Create marketing campaign (placeholder)
            campaign_type = serializer.validated_data.get('campaign_type', 'reminder')
            discount_percentage = serializer.validated_data.get('discount_percentage', 10)
            
            # This would integrate with email marketing system
            return Response({
                'success': True,
                'message': f'Created {campaign_type} campaign for {len(wishlist_ids)} items',
                'campaign_details': {
                    'type': campaign_type,
                    'discount': discount_percentage,
                    'target_count': len(wishlist_ids)
                }
            })
        
        elif operation == 'price_alert':
            # Set price alerts (placeholder)
            return Response({
                'success': True,
                'message': f'Set price alerts for {len(wishlist_ids)} items',
                'alert_count': len(wishlist_ids)
            })
        
        return Response({
            'success': False,
            'message': 'Invalid operation'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def product_stats(self, request):
        """Get wishlist statistics by product"""
        product_stats = WishlistProxy.objects.values(
            'product__id', 'product__title'
        ).annotate(
            wishlist_count=Count('id')
        ).order_by('-wishlist_count')[:20]
        
        stats_data = []
        for stat in product_stats:
            # Calculate additional metrics (placeholder)
            stats_data.append({
                'product_id': stat['product__id'],
                'product_title': stat['product__title'],
                'wishlist_count': stat['wishlist_count'],
                'conversion_rate': 15.5,  # Placeholder
                'average_days_to_conversion': 12.3,  # Placeholder
                'abandonment_rate': 25.8,  # Placeholder
                'revenue_potential': stat['wishlist_count'] * 45.50  # Placeholder
            })
        
        return Response(stats_data)
    
    @action(detail=False, methods=['get'])
    def customer_insights(self, request):
        """Get customer-specific wishlist insights"""
        customer_id = request.query_params.get('customer_id')
        if not customer_id:
            return Response(
                {'error': 'customer_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        customer_wishlists = WishlistProxy.objects.filter(
            customer_id=customer_id
        ).select_related('product__category')
        
        if not customer_wishlists.exists():
            return Response({
                'customer_id': customer_id,
                'total_wishlist_items': 0,
                'message': 'No wishlist items found for this customer'
            })
        
        # Calculate customer insights
        total_items = customer_wishlists.count()
        categories = customer_wishlists.values(
            'product__category__title'
        ).annotate(count=Count('id')).order_by('-count')
        
        avg_conversion_potential = sum(
            item.get_conversion_potential() for item in customer_wishlists
        ) / total_items
        
        insights = {
            'customer_id': int(customer_id),
            'total_wishlist_items': total_items,
            'average_time_to_purchase': 14.5,  # Placeholder
            'conversion_rate': 18.2,  # Placeholder
            'favorite_categories': [cat['product__category__title'] for cat in categories[:3]],
            'price_sensitivity': 'MEDIUM',  # Placeholder
            'engagement_level': 'HIGH' if avg_conversion_potential > 60 else 'MEDIUM',
            'average_conversion_potential': round(avg_conversion_potential, 1)
        }
        
        return Response(insights)
    
    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get wishlist trends over time"""
        days = int(request.query_params.get('days', 30))
        trends_data = WishlistAnalyticsService.get_wishlist_analytics(days)
        
        return Response(trends_data['wishlist_trends'])
