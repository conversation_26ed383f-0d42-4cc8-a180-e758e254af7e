from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import WishlistProxy
from apps.wishlist.models import Wishlist
from apps.products.models import Product, Category


class WishlistAnalyticsService:
    """Service for wishlist analytics and reporting"""
    
    @staticmethod
    def get_wishlist_analytics(days=30):
        """Get comprehensive wishlist analytics"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Base wishlist queryset for the time period
        wishlists = WishlistProxy.objects.filter(added_at__gte=cutoff_date)
        all_wishlists = WishlistProxy.objects.all()
        
        # Basic counts
        total_wishlist_items = all_wishlists.count()
        unique_customers = all_wishlists.values('customer').distinct().count()
        unique_products = all_wishlists.values('product').distinct().count()
        
        # Average items per customer
        if unique_customers > 0:
            average_items_per_customer = total_wishlist_items / unique_customers
        else:
            average_items_per_customer = 0
        
        # Conversion and abandonment rates
        conversion_rate = WishlistAnalyticsService._calculate_conversion_rate(all_wishlists)
        abandonment_rate = WishlistAnalyticsService._calculate_abandonment_rate(all_wishlists)
        
        # Top wishlist products
        top_products = WishlistAnalyticsService._get_top_wishlist_products()
        
        # Wishlist trends
        wishlist_trends = WishlistAnalyticsService._get_wishlist_trends(days)
        
        # Customer segments
        customer_segments = WishlistAnalyticsService._get_customer_segments(all_wishlists)
        
        # Category distribution
        category_distribution = WishlistAnalyticsService._get_category_distribution(all_wishlists)
        
        # Price range distribution
        price_range_distribution = WishlistAnalyticsService._get_price_range_distribution(all_wishlists)
        
        return {
            'total_wishlist_items': total_wishlist_items,
            'unique_customers': unique_customers,
            'unique_products': unique_products,
            'average_items_per_customer': round(average_items_per_customer, 2),
            'conversion_rate': round(conversion_rate, 2),
            'abandonment_rate': round(abandonment_rate, 2),
            'top_wishlist_products': top_products,
            'wishlist_trends': wishlist_trends,
            'customer_segments': customer_segments,
            'category_distribution': category_distribution,
            'price_range_distribution': price_range_distribution
        }
    
    @staticmethod
    def _calculate_conversion_rate(wishlists):
        """Calculate wishlist to purchase conversion rate"""
        # This would track actual conversions from wishlist to orders
        # For now, return a placeholder calculation
        total_items = wishlists.count()
        if total_items == 0:
            return 0.0
        
        # Placeholder: assume 15% conversion rate
        return 15.0
    
    @staticmethod
    def _calculate_abandonment_rate(wishlists):
        """Calculate wishlist abandonment rate"""
        total_items = wishlists.count()
        if total_items == 0:
            return 0.0
        
        abandoned_items = sum(1 for item in wishlists if item.is_abandoned())
        return (abandoned_items / total_items) * 100
    
    @staticmethod
    def _get_top_wishlist_products(limit=10):
        """Get most wishlisted products"""
        top_products = WishlistProxy.objects.values(
            'product__id', 'product__title'
        ).annotate(
            wishlist_count=Count('id')
        ).order_by('-wishlist_count')[:limit]
        
        return [
            {
                'product_id': item['product__id'],
                'product_title': item['product__title'],
                'wishlist_count': item['wishlist_count'],
                'conversion_potential': 75  # Placeholder
            }
            for item in top_products
        ]
    
    @staticmethod
    def _get_wishlist_trends(days):
        """Get wishlist trends over time"""
        trends = {}
        
        for i in range(days):
            date = timezone.now().date() - timedelta(days=i)
            date_key = date.isoformat()
            
            additions = WishlistProxy.objects.filter(
                added_at__date=date
            ).count()
            
            # Placeholder for removals and conversions
            trends[date_key] = {
                'additions': additions,
                'removals': max(0, additions - 2),  # Placeholder
                'conversions': max(0, additions // 5),  # Placeholder
                'net_change': additions - max(0, additions - 2)
            }
        
        return trends
    
    @staticmethod
    def _get_customer_segments(wishlists):
        """Get customer segment distribution"""
        segments = {'VIP': 0, 'LOYAL': 0, 'REPEAT': 0, 'NEW': 0, 'PROSPECT': 0}
        
        customers = wishlists.values('customer').distinct()
        for customer_data in customers:
            # This would use actual customer segmentation logic
            # For now, distribute randomly
            segments['LOYAL'] += 1
        
        return segments
    
    @staticmethod
    def _get_category_distribution(wishlists):
        """Get category distribution of wishlist items"""
        distribution = wishlists.filter(
            product__category__isnull=False
        ).values(
            'product__category__title'
        ).annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        return {
            item['product__category__title']: item['count']
            for item in distribution
        }
    
    @staticmethod
    def _get_price_range_distribution(wishlists):
        """Get price range distribution of wishlist items"""
        # This would analyze actual product prices
        # For now, return placeholder data
        return {
            '0-50': 120,
            '50-100': 85,
            '100-200': 65,
            '200-500': 45,
            '500+': 25
        }


class CustomerBehaviorService:
    """Service for customer behavior analysis"""
    
    @staticmethod
    def get_customer_behavior_analysis():
        """Get comprehensive customer behavior analysis"""
        # Customer wishlist patterns
        wishlist_patterns = CustomerBehaviorService._get_wishlist_patterns()
        
        # Seasonal trends
        seasonal_trends = CustomerBehaviorService._get_seasonal_trends()
        
        # Conversion patterns
        conversion_patterns = CustomerBehaviorService._get_conversion_patterns()
        
        # Abandonment patterns
        abandonment_patterns = CustomerBehaviorService._get_abandonment_patterns()
        
        # Cross-category preferences
        cross_category_preferences = CustomerBehaviorService._get_cross_category_preferences()
        
        # Price sensitivity analysis
        price_sensitivity = CustomerBehaviorService._get_price_sensitivity_analysis()
        
        return {
            'customer_wishlist_patterns': wishlist_patterns,
            'seasonal_trends': seasonal_trends,
            'conversion_patterns': conversion_patterns,
            'abandonment_patterns': abandonment_patterns,
            'cross_category_preferences': cross_category_preferences,
            'price_sensitivity_analysis': price_sensitivity
        }
    
    @staticmethod
    def _get_wishlist_patterns():
        """Analyze customer wishlist patterns"""
        return {
            'average_items_per_customer': 3.2,
            'most_active_time': '19:00-21:00',
            'peak_day': 'Sunday',
            'average_time_between_additions': '4.5 days',
            'repeat_wishlist_rate': 68.5
        }
    
    @staticmethod
    def _get_seasonal_trends():
        """Analyze seasonal wishlist trends"""
        return {
            'holiday_season_increase': 45.2,
            'summer_categories': ['Fashion', 'Sports', 'Travel'],
            'winter_categories': ['Electronics', 'Home', 'Books'],
            'back_to_school_spike': 'August',
            'gift_season_patterns': 'November-December'
        }
    
    @staticmethod
    def _get_conversion_patterns():
        """Analyze conversion patterns"""
        return {
            'average_time_to_conversion': '12.5 days',
            'best_conversion_categories': ['Electronics', 'Books'],
            'conversion_by_price_range': {
                '0-50': 25.3,
                '50-100': 18.7,
                '100-200': 12.4,
                '200+': 8.9
            },
            'discount_impact_on_conversion': 35.6
        }
    
    @staticmethod
    def _get_abandonment_patterns():
        """Analyze abandonment patterns"""
        return {
            'abandonment_threshold_days': 30,
            'high_abandonment_categories': ['Luxury', 'High-tech'],
            'abandonment_by_customer_segment': {
                'NEW': 45.2,
                'REPEAT': 32.1,
                'LOYAL': 18.7,
                'VIP': 12.3
            },
            'recovery_success_rate': 22.4
        }
    
    @staticmethod
    def _get_cross_category_preferences():
        """Analyze cross-category preferences"""
        return {
            'common_combinations': [
                ['Electronics', 'Accessories'],
                ['Fashion', 'Beauty'],
                ['Books', 'Stationery'],
                ['Sports', 'Health']
            ],
            'category_affinity_scores': {
                'Electronics-Accessories': 0.78,
                'Fashion-Beauty': 0.65,
                'Books-Stationery': 0.52
            }
        }
    
    @staticmethod
    def _get_price_sensitivity_analysis():
        """Analyze price sensitivity"""
        return {
            'price_elasticity': -1.2,
            'optimal_discount_range': '10-20%',
            'price_sensitive_segments': ['NEW', 'PROSPECT'],
            'premium_segments': ['VIP', 'LOYAL'],
            'discount_response_rate': 42.3
        }


class MarketingInsightsService:
    """Service for marketing insights and recommendations"""
    
    @staticmethod
    def get_marketing_insights():
        """Get comprehensive marketing insights"""
        # Conversion opportunities
        conversion_opportunities = MarketingInsightsService._get_conversion_opportunities()
        
        # Discount recommendations
        discount_recommendations = MarketingInsightsService._get_discount_recommendations()
        
        # Email campaign targets
        email_targets = MarketingInsightsService._get_email_campaign_targets()
        
        # Product promotion suggestions
        promotion_suggestions = MarketingInsightsService._get_promotion_suggestions()
        
        # Customer retention insights
        retention_insights = MarketingInsightsService._get_retention_insights()
        
        # Revenue potential
        revenue_potential = MarketingInsightsService._get_revenue_potential()
        
        return {
            'conversion_opportunities': conversion_opportunities,
            'discount_recommendations': discount_recommendations,
            'email_campaign_targets': email_targets,
            'product_promotion_suggestions': promotion_suggestions,
            'customer_retention_insights': retention_insights,
            'revenue_potential': revenue_potential
        }
    
    @staticmethod
    def _get_conversion_opportunities():
        """Get high-potential conversion opportunities"""
        high_potential_items = WishlistProxy.objects.all()[:20]  # Would filter by conversion potential
        
        opportunities = []
        for item in high_potential_items:
            opportunities.append({
                'wishlist_item_id': item.id,
                'customer_email': item.customer.user.email if item.customer.user else None,
                'product_title': item.product.title,
                'conversion_potential': item.get_conversion_potential(),
                'recommended_action': item.get_recommended_marketing_action(),
                'target_discount': item.get_target_discount(),
                'urgency_level': item.get_urgency_level(),
                'days_in_wishlist': item.get_days_in_wishlist(),
                'customer_segment': getattr(item.customer, 'get_customer_segment', lambda: 'UNKNOWN')()
            })
        
        return opportunities
    
    @staticmethod
    def _get_discount_recommendations():
        """Get discount recommendations by category/product"""
        return {
            'optimal_discount_by_category': {
                'Electronics': 15,
                'Fashion': 20,
                'Books': 10,
                'Home': 18
            },
            'time_sensitive_discounts': {
                'weekend_boost': 5,
                'holiday_special': 25,
                'flash_sale': 30
            },
            'customer_segment_discounts': {
                'NEW': 15,
                'REPEAT': 10,
                'LOYAL': 8,
                'VIP': 5
            }
        }
    
    @staticmethod
    def _get_email_campaign_targets():
        """Get email campaign targeting recommendations"""
        return {
            'high_conversion_potential': 245,
            'abandonment_recovery': 156,
            'price_drop_alerts': 89,
            'back_in_stock': 67,
            'seasonal_promotions': 334,
            'personalized_recommendations': 445
        }
    
    @staticmethod
    def _get_promotion_suggestions():
        """Get product promotion suggestions"""
        return [
            {
                'product_id': 123,
                'product_title': 'Wireless Headphones',
                'wishlist_count': 45,
                'suggested_promotion': 'Flash Sale',
                'expected_conversion_lift': 35.2,
                'revenue_potential': 2250.00
            },
            {
                'product_id': 456,
                'product_title': 'Smart Watch',
                'wishlist_count': 38,
                'suggested_promotion': 'Bundle Deal',
                'expected_conversion_lift': 28.7,
                'revenue_potential': 1890.00
            }
        ]
    
    @staticmethod
    def _get_retention_insights():
        """Get customer retention insights"""
        return {
            'at_risk_customers': 78,
            'retention_strategies': {
                'personalized_offers': 42.3,
                'loyalty_rewards': 38.7,
                'exclusive_access': 35.2
            },
            'churn_prevention_success_rate': 67.8,
            'lifetime_value_impact': 1250.00
        }
    
    @staticmethod
    def _get_revenue_potential():
        """Calculate revenue potential from wishlist"""
        total_wishlist_value = 0
        conversion_potential = 0
        
        for item in WishlistProxy.objects.all()[:100]:  # Sample for performance
            item_value = item.get_current_price()
            item_potential = item.get_conversion_potential() / 100
            
            total_wishlist_value += item_value
            conversion_potential += item_value * item_potential
        
        return {
            'total_wishlist_value': round(total_wishlist_value, 2),
            'estimated_conversion_revenue': round(conversion_potential, 2),
            'potential_uplift_with_campaigns': round(conversion_potential * 1.3, 2),
            'roi_projection': 4.2
        }
