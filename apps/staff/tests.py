from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from rest_framework.test import APIClient
from rest_framework import status
from .authorization.models import GroupMembership
from .authorization.services import GroupService

User = get_user_model()


class StaffRBACTestCase(TestCase):
    def setUp(self):
        # Create test users
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_active=False
        )

        # Create test group
        self.test_group = Group.objects.create(name='Test Group')

        self.client = APIClient()

    def test_staff_user_info(self):
        """Test getting staff user info (using core auth)"""
        # Login using core authentication (simulated)
        self.client.force_authenticate(user=self.staff_user)

        response = self.client.get('/api/staff/auth/user/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['email'], '<EMAIL>')

    def test_non_staff_access_denied(self):
        """Test that non-staff users cannot access staff endpoints"""
        # Try to access with regular user
        self.client.force_authenticate(user=self.regular_user)

        response = self.client.get('/api/staff/auth/user/')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_group_creation(self):
        """Test group creation by staff user"""
        # Authenticate as staff user (simulating core auth)
        self.client.force_authenticate(user=self.staff_user)

        # Create group
        response = self.client.post('/api/staff/groups/', {
            'name': 'New Test Group'
        })

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Group.objects.filter(name='New Test Group').exists())

    def test_add_user_to_group(self):
        """Test adding user to group"""
        # Authenticate as staff user (simulating core auth)
        self.client.force_authenticate(user=self.staff_user)

        # Add user to group
        response = self.client.post(f'/api/staff/groups/{self.test_group.id}/add_member/', {
            'user_id': self.staff_user.id,
            'notes': 'Test assignment'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertTrue(self.staff_user.groups.filter(id=self.test_group.id).exists())

    def test_group_service(self):
        """Test GroupService functionality"""
        group_service = GroupService()

        # Test adding user to group
        membership, created = group_service.add_user_to_group(
            user=self.staff_user,
            group=self.test_group,
            assigned_by=self.superuser,
            notes='Service test'
        )

        self.assertTrue(created)
        self.assertEqual(membership.user, self.staff_user)
        self.assertEqual(membership.group, self.test_group)
        self.assertTrue(membership.is_active)

        # Test getting user groups
        user_groups = group_service.get_user_groups(self.staff_user)
        self.assertIn('Test Group', user_groups)

        # Test removing user from group
        group_service.remove_user_from_group(
            user=self.staff_user,
            group=self.test_group,
            removed_by=self.superuser
        )

        # Check that membership is deactivated
        membership.refresh_from_db()
        self.assertFalse(membership.is_active)
        self.assertFalse(self.staff_user.groups.filter(id=self.test_group.id).exists())

    def test_add_regular_user_to_group(self):
        """Test adding regular (non-staff) user to group"""
        # Authenticate as staff user
        self.client.force_authenticate(user=self.staff_user)

        # Add regular user to group
        response = self.client.post(f'/api/staff/groups/{self.test_group.id}/add_member/', {
            'user_id': self.regular_user.id,
            'notes': 'Test assignment for regular user'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertTrue(self.regular_user.groups.filter(id=self.test_group.id).exists())

    def test_cannot_add_inactive_user_to_group(self):
        """Test that inactive users cannot be added to groups"""
        # Authenticate as staff user
        self.client.force_authenticate(user=self.staff_user)

        # Try to add inactive user to group
        response = self.client.post(f'/api/staff/groups/{self.test_group.id}/add_member/', {
            'user_id': self.inactive_user.id,
            'notes': 'Test assignment for inactive user'
        })

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('not active', str(response.data['errors']))
