from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count, Sum, Q
from .models import PaymentOptionProxy, PayPalOrderProxy, PaymentTransactionAudit, PaymentDispute
from .serializers import (
    PaymentOptionStaffSerializer, PayPalOrderStaffSerializer, PaymentTransactionAuditSerializer,
    PaymentDisputeSerializer, PaymentAnalyticsSerializer, TransactionMonitoringSerializer,
    BulkPaymentOperationSerializer, DisputeAssignmentSerializer, DisputeResolutionSerializer,
    PaymentOptionUpdateSerializer
)
from .permissions import (
    CanManagePayments, CanViewPaymentAnalytics, CanMonitorPayments,
    CanManageDisputes, CanAccessPayPalData
)
from .services import PaymentAnalyticsService, PaymentMonitoringService, PaymentManagementService
from apps.staff.common.utils import paginate_queryset


class PaymentOptionStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff payment option management
    """
    queryset = PaymentOptionProxy.objects.all()
    permission_classes = [CanManagePayments]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'slug']
    ordering_fields = ['name', 'is_active']
    ordering = ['name']
    
    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return PaymentOptionUpdateSerializer
        return PaymentOptionStaffSerializer
    
    @action(detail=False, methods=['get'], permission_classes=[CanViewPaymentAnalytics])
    def analytics(self, request):
        """Get payment analytics data"""
        days = int(request.query_params.get('days', 30))
        analytics_data = PaymentAnalyticsService.get_payment_analytics(days)
        
        serializer = PaymentAnalyticsSerializer(analytics_data)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def transactions(self, request, pk=None):
        """Get recent transactions for a payment method"""
        payment_option = self.get_object()
        days = int(request.query_params.get('days', 30))
        
        recent_transactions = payment_option.get_recent_transactions(days)
        
        transaction_data = [
            {
                'id': order.id,
                'customer_email': order.customer.user.email if order.customer else None,
                'total': float(order.total),
                'status': order.payment_status,
                'placed_at': order.placed_at,
            }
            for order in recent_transactions
        ]
        
        return Response({
            'payment_method': payment_option.name,
            'transactions': transaction_data,
            'count': len(transaction_data)
        })
    
    @action(detail=False, methods=['get'], permission_classes=[CanMonitorPayments])
    def monitoring(self, request):
        """Get real-time payment monitoring data"""
        monitoring_data = PaymentMonitoringService.get_monitoring_data()
        
        serializer = TransactionMonitoringSerializer(monitoring_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_operations(self, request):
        """Perform bulk operations on payments"""
        serializer = BulkPaymentOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        payment_ids = serializer.validated_data['payment_ids']
        operation = serializer.validated_data['operation']
        
        if operation == 'export':
            # Export payment data (placeholder)
            return Response({
                'success': True,
                'message': 'Export functionality would be implemented here',
                'count': len(payment_ids)
            })
        else:
            # Other bulk operations
            result = PaymentManagementService.bulk_process_payments(
                payment_ids=payment_ids,
                operation=operation,
                performed_by=request.user,
                **serializer.validated_data
            )
            
            return Response({
                'success': True,
                'message': f'Bulk {operation} completed',
                'processed_count': result['processed_count'],
                'errors': result['errors'],
                'total_requested': result['total_requested']
            })


class PayPalOrderStaffViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for staff PayPal order management (read-only)
    """
    queryset = PayPalOrderProxy.objects.all().select_related('order__customer__user')
    serializer_class = PayPalOrderStaffSerializer
    permission_classes = [CanAccessPayPalData]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['paypal_order_id', 'order__customer__user__email']
    ordering_fields = ['created_at', 'updated_at', 'status']
    ordering = ['-created_at']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def dispute_info(self, request, pk=None):
        """Get dispute information for a PayPal order"""
        paypal_order = self.get_object()
        dispute_info = paypal_order.get_dispute_info()
        
        return Response(dispute_info)


class PaymentTransactionAuditViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for payment transaction audit logs (read-only)
    """
    queryset = PaymentTransactionAudit.objects.all().select_related('staff_user')
    serializer_class = PaymentTransactionAuditSerializer
    permission_classes = [CanMonitorPayments]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['transaction_id', 'payment_method', 'action']
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by payment method if provided
        payment_method = self.request.query_params.get('payment_method')
        if payment_method:
            queryset = queryset.filter(payment_method=payment_method)
        
        # Filter by action if provided
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)
        
        return queryset


class PaymentDisputeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for payment dispute management
    """
    queryset = PaymentDispute.objects.all().select_related('order__customer__user', 'assigned_to')
    serializer_class = PaymentDisputeSerializer
    permission_classes = [CanManageDisputes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['dispute_id', 'order__customer__user__email', 'reason']
    ordering_fields = ['created_at', 'updated_at', 'amount']
    ordering = ['-created_at']
    http_method_names = ['get', 'patch', 'post']  # No delete for disputes
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by dispute type if provided
        dispute_type = self.request.query_params.get('dispute_type')
        if dispute_type:
            queryset = queryset.filter(dispute_type=dispute_type)
        
        # Filter by assigned user if provided
        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)
        
        return queryset
    
    @action(detail=False, methods=['post'])
    def assign_disputes(self, request):
        """Assign disputes to staff members"""
        serializer = DisputeAssignmentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        dispute_ids = serializer.validated_data['dispute_ids']
        assigned_to_id = serializer.validated_data['assigned_to']
        notes = serializer.validated_data.get('notes', '')
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            assigned_to = User.objects.get(id=assigned_to_id, is_staff=True)
        except User.DoesNotExist:
            return Response(
                {'error': 'Invalid staff user ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = PaymentDispute.objects.filter(
            id__in=dispute_ids
        ).update(assigned_to=assigned_to)
        
        return Response({
            'success': True,
            'message': f'Assigned {updated_count} disputes to {assigned_to.email}',
            'updated_count': updated_count
        })
    
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve a dispute"""
        dispute = self.get_object()
        serializer = DisputeResolutionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        dispute.resolution_notes = serializer.validated_data['resolution_notes']
        dispute.status = serializer.validated_data['status']
        
        if dispute.status in ['RESOLVED', 'CLOSED']:
            dispute.resolved_at = timezone.now()
        
        dispute.save()
        
        return Response({
            'success': True,
            'message': f'Dispute {dispute.dispute_id} has been {dispute.status.lower()}',
            'dispute': PaymentDisputeSerializer(dispute).data
        })
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get dispute summary statistics"""
        queryset = self.get_queryset()
        
        summary = {
            'total_disputes': queryset.count(),
            'open_disputes': queryset.filter(status='OPEN').count(),
            'under_review': queryset.filter(status='UNDER_REVIEW').count(),
            'resolved_disputes': queryset.filter(status='RESOLVED').count(),
            'closed_disputes': queryset.filter(status='CLOSED').count(),
            'by_type': dict(queryset.values_list('dispute_type').annotate(Count('id'))),
            'average_resolution_time': None,  # Would calculate from resolved disputes
        }
        
        return Response(summary)
