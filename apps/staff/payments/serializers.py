from rest_framework import serializers
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from .models import PaymentOptionProxy, PayPalOrderProxy, PaymentTransactionAudit, PaymentDispute
from apps.customers.serializers import SimpleCustomerSerializer


class PaymentOptionStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff payment option management"""
    usage_count = serializers.SerializerMethodField()
    success_rate = serializers.SerializerMethodField()
    total_processed = serializers.SerializerMethodField()
    recent_transactions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentOptionProxy
        fields = [
            'id', 'name', 'slug', 'is_active',
            'usage_count', 'success_rate', 'total_processed',
            'recent_transactions_count'
        ]
    
    def get_usage_count(self, obj):
        return obj.get_usage_count()
    
    def get_success_rate(self, obj):
        return obj.get_success_rate()
    
    def get_total_processed(self, obj):
        return obj.get_total_processed()
    
    def get_recent_transactions_count(self, obj):
        return obj.get_recent_transactions(days=30).count()


class PayPalOrderStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff PayPal order management"""
    customer_email = serializers.SerializerMethodField()
    order_total = serializers.SerializerMethodField()
    processing_time = serializers.SerializerMethodField()
    is_disputed = serializers.SerializerMethodField()
    
    class Meta:
        model = PayPalOrderProxy
        fields = [
            'id', 'order', 'paypal_order_id', 'status',
            'customer_email', 'order_total', 'processing_time',
            'is_disputed', 'created_at', 'updated_at'
        ]
    
    def get_customer_email(self, obj):
        return obj.order.customer.user.email if obj.order and obj.order.customer else None
    
    def get_order_total(self, obj):
        return float(obj.order.total) if obj.order else 0
    
    def get_processing_time(self, obj):
        return obj.get_processing_time()
    
    def get_is_disputed(self, obj):
        return obj.is_disputed()


class PaymentTransactionAuditSerializer(serializers.ModelSerializer):
    """Serializer for payment transaction audit logs"""
    staff_user_email = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentTransactionAudit
        fields = [
            'id', 'transaction_id', 'payment_method', 'order_id',
            'customer_id', 'amount', 'status', 'action',
            'staff_user_email', 'notes', 'created_at'
        ]
    
    def get_staff_user_email(self, obj):
        return obj.staff_user.email if obj.staff_user else None


class PaymentDisputeSerializer(serializers.ModelSerializer):
    """Serializer for payment dispute management"""
    customer_email = serializers.SerializerMethodField()
    assigned_to_email = serializers.SerializerMethodField()
    days_open = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentDispute
        fields = [
            'id', 'dispute_id', 'order', 'dispute_type', 'status',
            'amount', 'reason', 'customer_email', 'assigned_to_email',
            'days_open', 'resolution_notes', 'created_at', 'updated_at',
            'resolved_at'
        ]
    
    def get_customer_email(self, obj):
        return obj.order.customer.user.email if obj.order.customer else None
    
    def get_assigned_to_email(self, obj):
        return obj.assigned_to.email if obj.assigned_to else None
    
    def get_days_open(self, obj):
        return (timezone.now() - obj.created_at).days


class PaymentAnalyticsSerializer(serializers.Serializer):
    """Serializer for payment analytics data"""
    total_transactions = serializers.IntegerField()
    successful_transactions = serializers.IntegerField()
    failed_transactions = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    average_transaction_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    success_rate = serializers.FloatField()
    payment_method_distribution = serializers.DictField()
    daily_revenue = serializers.DictField()
    dispute_rate = serializers.FloatField()
    refund_rate = serializers.FloatField()
    top_failure_reasons = serializers.ListField()


class TransactionMonitoringSerializer(serializers.Serializer):
    """Serializer for transaction monitoring data"""
    recent_transactions = serializers.ListField()
    failed_transactions = serializers.ListField()
    suspicious_transactions = serializers.ListField()
    high_value_transactions = serializers.ListField()
    payment_gateway_status = serializers.DictField()
    alerts = serializers.ListField()


class BulkPaymentOperationSerializer(serializers.Serializer):
    """Serializer for bulk payment operations"""
    payment_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(choices=[
        ('refund', 'Process Refund'),
        ('dispute', 'Mark as Disputed'),
        ('investigate', 'Flag for Investigation'),
        ('export', 'Export Transaction Data'),
    ])
    reason = serializers.CharField(max_length=500, required=False)
    refund_amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        required=False
    )


class DisputeAssignmentSerializer(serializers.Serializer):
    """Serializer for dispute assignment"""
    dispute_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    assigned_to = serializers.IntegerField()
    notes = serializers.CharField(max_length=1000, required=False)


class DisputeResolutionSerializer(serializers.Serializer):
    """Serializer for dispute resolution"""
    resolution_notes = serializers.CharField(max_length=2000)
    status = serializers.ChoiceField(choices=[
        ('RESOLVED', 'Resolved'),
        ('CLOSED', 'Closed'),
    ])
    refund_amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False
    )


class PaymentOptionUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating payment options"""
    
    class Meta:
        model = PaymentOptionProxy
        fields = ['name', 'slug', 'is_active']
    
    def validate_slug(self, value):
        """Validate slug uniqueness"""
        if self.instance and self.instance.slug != value:
            if PaymentOptionProxy.objects.filter(slug=value).exists():
                raise serializers.ValidationError("Payment option with this slug already exists.")
        return value
